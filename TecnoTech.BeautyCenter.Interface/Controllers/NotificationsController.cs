using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.ComponentModel;
using System.Net.Mail;
using System.Threading.Tasks;
using System;
using TecnoTech.BeautyCenter.DTOs.DTOModel;
using TecnoTech.BeautyCenter.Services.Services;

namespace TecnoTech.BeautyCenter.Interface.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class NotificationsController : ControllerBase
    {
        private readonly Microsoft.AspNetCore.Hosting.IHostingEnvironment _hostingEnvironment;
        private readonly INotificationService _notificationService;

        public NotificationsController(Microsoft.AspNetCore.Hosting.IHostingEnvironment hostingEnvironment, INotificationService notificationService)
        {
            _hostingEnvironment = hostingEnvironment;
            _notificationService = notificationService; 
        }
        #region Queries (Get Notifications By Filtter) 
        /// <summary>
        /// URL:BaseURL/Notifications/Get
        /// Method:Get
        /// Handler: namespace EDMS.Application.Services.Settings.Notificationses.Queries.GetNotificationses 
        /// </summary>
        /// <param name="QueryNotificationsModel"></param>
        /// <returns>List Of Notificationss </returns>
        [Route("GetNotifications")]
        [HttpGet]
        public async Task<IActionResult> Get()
        {
            var res = await _notificationService.GetAll();
            return Ok(res);
        }

        [Route("GetNotificationById")]
        [HttpGet()]
        public async Task<ActionResult> GetById(int userId)
        {
            return Ok("");
        }
        #endregion


        #region Commands
        /// <summary>
        /// URL:BaseURL/Notifications/Create
        /// Method:Create
        /// Handler : namespace EDMS.Application.Services.Settings.Notificationses.Commands.Create
        /// </summary>
        /// <param name="NotificationsModel"></param>
        /// <returns><Response<bool>></returns>
        /// 
        [Route("Insert")]
        [HttpPost]
        public async Task<ActionResult> Insert(NotificationsDTO entity)
        {
            if (ModelState.IsValid)
            {
                var Result = await _notificationService.Insert(entity);
                if (Result.State)
                {
                    return Ok(Result);
                }
                else
                {

                    return BadRequest(Result.Message);
                }
            }
            return BadRequest(ModelState);
        }
        /// <summary>
        /// URL:BaseURL/Notifications/Update
        /// Method:update
        /// Handler : namespace EDMS.Application.Services.Settings.Notificationses.Commands.Update
        /// </summary>
        /// <param name="EditNotificationsModel"></param>
        /// <returns> Response<bool></returns>
        [Route("update")]
        [HttpPost]
        public async Task<ActionResult> update(NotificationsEditDTO entity)
        {
            if (ModelState.IsValid)
            {
                var Result = await _notificationService.Update(entity);
                if (Result.State)
                {
                    return Ok(Result);
                }
                else
                {

                    return BadRequest(Result.Message);
                }
            }
            return BadRequest(ModelState);
        }
        #endregion
    }
}
