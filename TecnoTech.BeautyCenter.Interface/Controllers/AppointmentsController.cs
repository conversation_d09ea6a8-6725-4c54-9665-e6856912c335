using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.Extensions.Logging;
using TecnoTech.BeautyCenter.DTOs.DTOModel;
using TecnoTech.BeautyCenter.Services.Services;


namespace Project.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class AppointmentsController : ControllerBase
    {
        private readonly ILogger<AppointmentsController> _logger;
        private readonly ISlotsService _slotsService;
        private readonly IServicesService _ServicesService;
        private readonly IAppointmentService _AppointmentService;
        public AppointmentsController(ILogger<AppointmentsController> logger, ISlotsService slotsService, IAppointmentService appointmentService, IServicesService servicesService)
        {
            _slotsService = slotsService;
            _logger = logger;
            _ServicesService = servicesService;
            _AppointmentService = appointmentService;
        }
        // GET: api/<ValuesController>
        [HttpPost("CreateShift")]
        //public async Task<IActionResult> CreateShift(List<RequestShiftDTO> requestShiftList)
        public async Task<IActionResult> CreateShift(LongShiftDTO longShiftDT)
        {
            try
            {
                _logger.LogError("Inputs CreateSlots", longShiftDT);

                if (ModelState.IsValid)
                {
                    var result = await _slotsService.CreateShift(longShiftDT);
                    if (result.State)
                    {
                        return Ok(result);
                    }
                    else
                    {
                        _logger.LogError("Errors CreateSlots", result);

                        return BadRequest(result.Message);
                    }
                }
                else
                {
                    _logger.LogError("Model Is not valid");
                    return BadRequest("Model Is not valid");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex.Message);
                return BadRequest(ex.Message);
            }
        }
        // GET api/<ValuesController>/5
        [HttpPost("AvailableSlots")]
        public async Task<IActionResult> AvailableSlots(AppointmentsRequestDTO requestDTO)
        {
            _logger.LogError("Inputs AvailableSlots", requestDTO);

            if (ModelState.IsValid)
            {
                var result = await _slotsService.GetAllAvailableSlots(requestDTO);
                var res = new Dictionary<string, dynamic>();
                if (result.State)
                {
                    res.Add("Value", result);
                    return Ok(res);
                }
                else
                {
                    _logger.LogError("Errors AvailableSlots", result);

                    res.Add("Error", result.Message);
                    return BadRequest(result);
                }
            }
            else
            {
                ModelState.AddModelError("Errors", "add all required field please");
                return BadRequest(ModelState);
            }
        }
        // POST api/<ValuesController>
        // create
        [HttpPost("BookingAppointment")]
        public async Task<IActionResult> BookingAppointment([FromBody] BookingDTO Model)
        {
            if (ModelState.IsValid)
            {
                var result = await _AppointmentService.BookingAppointment(Model);
                if (result.State)
                {
                    return Ok(result);
                }
                else
                {
                    ModelState.AddModelError("Errors", result.Message);

                    return BadRequest(ModelState);
                }
            }
            else
            {
                ModelState.AddModelError("Errors", "Model is not  complete");
                return BadRequest(ModelState);
            }
        }
        // PUT api/<ValuesController>/5
        [HttpGet("GetAppointmentToApprove")]
        public async Task<IActionResult> GetAppointmentToApprove(string userId)
        {
            var result = await _AppointmentService.GetAppointmentToApprove(userId);
            return Ok(result);
        }
        [HttpGet("GetConfirmidAppointment")]
        public async Task<IActionResult> GetConfirmidAppointment(string userId)
        {
            var result = await _AppointmentService.GetConfirmidAppointment(userId);
            return Ok(result);
        }
        [HttpPost("ApproveAnAppointment")]
        public async Task<IActionResult> ApproveAnAppointment(int AppointmentId)
        {
            _logger.LogError("Inputs ApproveAnAppointment", AppointmentId);
            var result = await _AppointmentService.ApproveAnAppointment(AppointmentId);
            return Ok(result);
        }
        // DELETE api/<ValuesController>/5
        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(int AppointmentId)
        {
            _logger.LogError("Inputs Delete", AppointmentId);

            var res = await _AppointmentService.DeleteAppointment(AppointmentId);
            if (res.State)
            {
                return Ok(res);
            }
            else
            {
                _logger.LogError("Errors Delete", res);

                return BadRequest(res.Message);
            }
        }
        //Get all appointment
        [HttpGet("GetAppointmentsByUserId")]
        public async Task<IActionResult> GetAppointmentsByUserId(string userId)
        {
            _logger.LogError("Inputs GetAppointmentsByUserId", userId);
            var res = await _AppointmentService.GetAppointmentsByUserId(userId);
            return Ok(res);
        }
        //Get Pending appointment
        [HttpGet("GetPendingAppointmentsByUserId")]
        public async Task<IActionResult> GetPendingAppointmentsByUserId(string userId)
        {
            _logger.LogError("Inputs GetAppointmentsByUserId", userId);
            var res = await _AppointmentService.GetPendingAppointmentsByUserId(userId);
            return Ok(res);
        }

        //Get Confirmed appointment
        [HttpGet("GetConfirmedAppointmentsByUserId")]
        public async Task<IActionResult> GetConfirmedAppointmentsByUserId(string userId)
        {
            _logger.LogError("Inputs GetAppointmentsByUserId", userId);
            var res = await _AppointmentService.GetConfirmedAppointmentsByUserId(userId);
            return Ok(res);
        }
        [HttpPost("CancelBooking")]
        public async Task<IActionResult> CancelBooking(int appointmentId)
        {
            _logger.LogError("Inputs CancelBooking", appointmentId);

            {
                var res = await _AppointmentService.CancelBooking(appointmentId);
                if (res.State)
                {

                    return Ok(res.Object);
                }
                else
                {
                    return BadRequest(res.Message);
                }
            }
        }
        [HttpPost("CompletelBooking")]
        public async Task<IActionResult> CompletelBooking(int appointmentId)
        {
            var res = await _AppointmentService.CompleteBooking(appointmentId);
            if (res.State)
            {

                return Ok(res.Object);
            }
            else
            {
                _logger.LogError("Errors CompletelBooking", res);

                return BadRequest(res.Message);
            }
        }
        [HttpGet("BookingHistory")]
        public async Task<IActionResult> BookingHistory(string userId)
        {
            if (userId == null)
            {
                var res = await _AppointmentService.AllBookingHistory();
                return Ok(res);
            }
            else
            {
                var res2 = await _AppointmentService.AllBookingHistory(userId);
                return Ok(res2);
            }

        }
        [HttpPost("BookingHistoryByFillter")]
        public async Task<IActionResult> BookingHistory(BookingHistorySearchFiltrationDTO filtrationDTO)
        {

            var res = await _AppointmentService.AllBookingHistory(filtrationDTO);
            var tre = res;
            return Ok(res);

        }
        [HttpGet("BookingHistoryByEmployeeId")]
        public async Task<IActionResult> AllBookingHistoryByEmployeeId(string userId)
        {
            if (userId != null)
            {
                var res = await _AppointmentService.AllBookingHistoryByEmployeeId(userId);
                return Ok(res);
            }
            else
            {
                var res2 = await _AppointmentService.AllBookingHistoryByEmployeeId(String.Empty);
                return Ok(res2);
            }

        }
        [HttpGet("GetAppointmentsByMobile")]
        public async Task<IActionResult> GetAppointmentsByMobile(string Mobile)
        {
            _logger.LogError("Inputs GetAppointmentsByMobile", Mobile);

            var res = await _AppointmentService.GetAppointmentsByMobile(Mobile);
            return Ok(res);
        }

        [HttpPost("GetShiftState")]
        public async Task<IActionResult> GetShiftState(checkIfShiftIsExcistDTO checkIf)
        {
            var res = await _slotsService.ResultfShiftState(checkIf);
            return Ok(res);
        }

    }
}
