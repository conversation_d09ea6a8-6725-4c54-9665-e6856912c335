using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TecnoTech.BeautyCenter.DTOs.Administration;
using TecnoTech.BeautyCenter.DTOs.DTOModel;
using TecnoTech.BeautyCenter.DTOs.DTOModel.Requests.FromMobile;
using TecnoTech.BeautyCenter.DTOs.DTOModel.Response;
using TecnoTech.BeautyCenter.Services.Services;


namespace TecnoTech.BeautyCenter.Interface.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class AccountentController : ControllerBase
    {
        private readonly ILogger<AccountentController> _logger;
        private readonly IAccountentService _AccountentService;
        public AccountentController(ILogger<AccountentController> logger, IAccountentService accountentService)
        {
            _AccountentService = accountentService;
            _logger = logger;
        }
        [HttpPost]
        [Route("Register")]
        public async Task<IActionResult> Register([FromBody] RegistrationRequestDTO user)
        {
            _logger.LogError("Inputs Register", user);

            if (ModelState.IsValid)
            {
                var Result = await _AccountentService.Register(user);
                if (Result.Result)
                {
                    return Ok(Result);
                }
                else
                {
                    var reternDTO = new JsonResult(new RegistrationResponseDTO()
                    {
                        Result = false,
                        Errors = Result.Errors.ToList()
                    })
                    { StatusCode = 500 };

                    return BadRequest(reternDTO);
                }
            }
            else
            {
                _logger.LogError("Model State IsValid when Register");
                return BadRequest(new RegistrationResponseDTO()
                {
                    Result = false,
                    Errors = new List<string>() { "Invalid payload" }
                });
            }
        }
        [HttpPost]
        [Route("ForgetPassword")]
        public async Task<IActionResult> ForgetPassword([FromBody] ResetPassword user)
        {
            _logger.LogError("Inputs ForgetPassword", user);

            if (ModelState.IsValid)
            {
                var Result = await _AccountentService.ResetPassword(user);
                if (Result.Result)
                {
                    return Ok(Result);
                }
                else
                {             
                    return BadRequest(Result);
                }
            }
            else
            {
                _logger.LogError("Model State IsValid when ResetPassword");
                return BadRequest("Model State IsValid when ResetPassword");           
            }
        }
        [HttpPost]
        [Route("Login")]
        public async Task<IActionResult> Login([FromBody] LoginRequestDTO user)
        {
            _logger.LogError("Inputs Login", user);

            if (ModelState.IsValid)
            {
                var Result = await _AccountentService.Login(user);
                if (Result.Result)
                {
                    return Ok(Result);
                }
                else
                {
                    var reternDTO = new JsonResult(new RegistrationResponseDTO()
                    {
                        Result = false,
                        Errors = Result.Errors.ToList()
                    })
                    { StatusCode = 500 };

                    return BadRequest(reternDTO);
                }
            }
            _logger.LogError("Invalid payload");
            return BadRequest(new RegistrationResponseDTO()
            {
                Result = false,
                Errors = new List<string>(){
                    "Invalid payload"
                }
            });
        }
        [HttpPost]
        [Route("LoginFingerPrint")]
        public async Task<IActionResult> LoginFingerPrint([FromBody] LoginFingerPrint user)
        {
            _logger.LogError("Inputs LoginFingerPrint", user);

            if (ModelState.IsValid)
            {
                var Result = await _AccountentService.LoginFingerPrint(user);
                if (Result.Result)
                {
                    return Ok(Result);
                }
                else
                {
                    var reternDTO = new JsonResult(new RegistrationResponseDTO()
                    {
                        Result = false,
                        Errors = Result.Errors.ToList()
                    })
                    { StatusCode = 500 };

                    return BadRequest(reternDTO);
                }
            }
            _logger.LogError("Invalid payload");
            return BadRequest(new RegistrationResponseDTO()
            {
                Result = false,
                Errors = new List<string>(){
                    "Invalid payload"
                }
            });
        }
        [HttpGet(nameof(Logout))]
        public async Task<ActionResult> Logout()
        {
            await _AccountentService.Logout();
            return Ok();
        }
        [HttpGet("get")]
        public async Task<ActionResult> ListEmployeeUsers()
        {
            var ListUser = await _AccountentService.ListEmployeeUsers();
            if (ListUser.Count != 0)
            {
                return Ok(ListUser);
            }
            else
            {
                _logger.LogError("ListEmployeeUsers Errors", ListUser);

                return Content("List Of  Employees is Empety");
            }
        }
        [HttpPost("Create")]
        public async Task<IActionResult> CreateUser(UserDTO user)
        {
            var CreateUser = await _AccountentService.CreateUser(user);
            if (CreateUser.State)
            {
                return Ok(CreateUser);
            }
            else
            {
                _logger.LogError("CreateUser Errors", CreateUser);

                return Content(CreateUser.Message);
            }
        }
        [HttpGet("Details")]
        public async Task<IActionResult> Details(string id)
        {
             var Details = await _AccountentService.Details(id);
            if (Details.State)
            {
                return Ok(Details.Object);
            }
            else
            {
                _logger.LogError("UserDetails Errors", Details);

                return Content(Details.Message);
            }

        }
        [HttpPost("Update")]
        public async Task<IActionResult> Update(EditUserDTO model)
        {
            _logger.LogError("Inputs Update", model);

            var Details = await _AccountentService.Update(model);
            if (Details.State)
            {
                return Ok(Details.Object);
            }
            else
            {
                _logger.LogError("Errors Update", Details);

                return BadRequest(Details.Object);
            }
        }
        [HttpPost("SignEmployeeToServices")]
        public async Task<IActionResult> SignEmployeeToServices(SignEmployeeToServicesDTO Model)
        {
            _logger.LogError("Inputs SignEmployeeToServices", Model);

            var Details = await _AccountentService.SignEmployeeToServices(Model);
            if (Details.State)
            {
                return Ok(Details.Object);
            }
            else
            {
                _logger.LogError("Errors SignEmployeeToServices", Details);

                return Content(Details.Message);
            }
        }
        [HttpPost("Delete")]
        public async Task<IActionResult> DeleteUser(string id)
        {
            _logger.LogError("Inputs DeleteUser", id);

            var Details = await _AccountentService.DeleteUser(id);
            if (Details.State)
            {
                return Ok(Details.Object);
            }
            else
            {
                _logger.LogError("Errors DeleteUser", Details);

                return BadRequest(Details.Object);
            }
        }

        [HttpGet("GetCustomer")]
        public async Task<ActionResult> GetCustomer()
        {
            var ListCustomer = await _AccountentService.GetCustomer();
            if (ListCustomer.Count != 0)
            {
                return Ok(ListCustomer);
            }
            else
            {
                _logger.LogError("Errors GetCustomer", ListCustomer);
                return Content("List Of Customer is Empety");
            }
        }
        [HttpGet("GetUsersByName")]
        public async Task<IActionResult> GetUsersByName(string name)
        {
            _logger.LogError("Inputs GetUsersByName", name);

            var res = await _AccountentService.GetUsersByName(name);
            return Ok(res);
        }

    }
}
