using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;
using TecnoTech.BeautyCenter.Services.Services;

// For more information on enabling Web API for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860

namespace TecnoTech.BeautyCenter.Interface.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class CustomerController : ControllerBase
    {
        private readonly ICustomerService customerService;
        public CustomerController(ICustomerService customerService)
        {
            this.customerService = customerService;
        }
        // GET: api/<CustomerController>
        [HttpGet(nameof(getAllBillCompleteJobByUserId))]
        public async Task<IEnumerable<dynamic>> getAllBillCompleteJobByUserId(string CustomerId)
        {
            var PreviousCompleteJob = await customerService.getAllBillCompleteJobByUserId(CustomerId);
            return PreviousCompleteJob;
        }

         [HttpGet(nameof(getAllBillCompleteJob))]
        public async Task<IEnumerable<dynamic>> getAllBillCompleteJob()
        {
            var AllCompleteJob = await customerService.getAllBillCompleteJob();
            return AllCompleteJob;
        }   
        [HttpGet(nameof(getAllCompleteJobByUserId))]
        public async Task<IEnumerable<dynamic>> getAllCompleteJobByUserId(string CustomerId)
        {
            var PreviousCompleteJob = await customerService.getAllCompleteJobByUserId(CustomerId);
            return PreviousCompleteJob;
        }

        [HttpGet(nameof(getAllCompleteJob))]
        public async Task<IEnumerable<dynamic>> getAllCompleteJob()
        {
            var AllCompleteJob = await customerService.getAllCompleteJob();
            return AllCompleteJob;
        }
        //ce72444e-151e-4779-afc1-2fbc598d471d
        [HttpGet(nameof(GetPoints))]
        public async Task<pointCashDTO> GetPoints(string CustomerId)
        {
            pointCashDTO AllPoint = await customerService.GetPoints(CustomerId);
            
            return AllPoint;
        }
    }
}
