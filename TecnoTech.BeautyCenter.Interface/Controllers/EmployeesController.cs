using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System.Threading.Tasks;
using TecnoTech.BeautyCenter.DAL.SqlServer.Models;
using TecnoTech.BeautyCenter.DTOs.DTOModel;
using TecnoTech.BeautyCenter.Services.Services;

namespace TecnoTech.BeautyCenter.Interface.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class EmployeesController : ControllerBase
    {
        private readonly ILogger<EmployeesController> _logger;
        private readonly IEmployeesService _EmployeesService;
        private readonly ISlotsService _ISlotsService;

        public EmployeesController(ILogger<EmployeesController> logger, IEmployeesService employeesService, ISlotsService iSlotsService)
        {
            _logger = logger;
            _EmployeesService = employeesService;
            _ISlotsService = iSlotsService;
        }

        [HttpGet("GetEmployeeByServiceId")]
        public async Task<IActionResult> GetEmployeeByServiceId(int ServiceId)
        {
            _logger.LogError("Inputs GetEmployeeByServiceId", ServiceId);

            var result = await _EmployeesService.GetEmployeeByServiceId(ServiceId);
            if (result.State == true)
            {
                return Ok(result);
            }
            else
            {
                _logger.LogError("Errors GetEmployeeByServiceId", result);

                return BadRequest(result.Object);
            }
        }

        [HttpGet("GetEmployeeShift")]
        public async Task<IActionResult> GetEmployeeShift(string EmployeeId)
        {
            _logger.LogError("Inputs GetEmployeeShift", EmployeeId);

            var result = await _EmployeesService.GetEmployeeShift(EmployeeId);
            if (result.State == true)
            {
                return Ok(result);
            }
            else
            {
                _logger.LogError("Errors GetEmployeeShift", result);
                return BadRequest(result.Object);
            }

        }


        [HttpGet("GetSlotsEmployeeShift")]
        public async Task<IActionResult> GetSlotsEmployeeShift(string EmployeeId)
        {
            _logger.LogError("Inputs GetSlotsEmployeeShift", EmployeeId);

            var result = await _EmployeesService.GetSlotsEmployeeShift(EmployeeId);
            if (result.State == true)
            {
                return Ok(result);
            }
            else
            {
                _logger.LogError("Errors GetEmployeeShift", result);
                return BadRequest(result.Object);
            }

        }

        [HttpGet("GetAllEmployeesShifts")]
        public async Task<IActionResult> GetAllEmployeesShifts()
        {
            var result = await _EmployeesService.GetAllEmployeesShifts();
            if (result.State == true)
            {
                return Ok(result);
            }
            else
            {
                _logger.LogError("Errors GetEmployeeShift", result);
                return BadRequest(result.Object);
            }
        }

        [HttpPost("CheckEmployeeShift")]
        public async Task<IActionResult> CheckSwapEmployeeShift(ShiftEmpDTO shift)
        {
            _logger.LogError("Inputs CheckSwapEmployeeShift", shift);

            var result = await _EmployeesService.CheckSwapEmployeeShift(shift);
            if (result.State == true)
            {
                return Ok(result);
            }
            else
            {
                _logger.LogError("Errors CheckSwapEmployeeShift", result);
                return BadRequest(result.Object);
            }

        }

        [HttpPost("CheckChangeShift")]
        public async Task<IActionResult> CheckChangeShift(ShiftEmpDTO shift)
        {
            _logger.LogError("Inputs CheckChangeShift", shift);

            var result = await _EmployeesService.CheckChangeShift(shift);
            if (result.State == true)
            {
                return Ok(result);
            }
            else
            {
                _logger.LogError("Errors CheckChangeShift", result);
                return BadRequest(result.Object);
            }

        }
     
        [HttpPost("UpdateEmployeeShift")]
        public async Task<IActionResult> UpdateEmployeeShift(ShiftDTO shift)
        {
            _logger.LogError("Inputs UpdateEmployeeShift", shift);

            var result = await _EmployeesService.UpdateEmployeeShift(shift);
            if (result.State == true)
            {
                return Ok(result);
            }
            else
            {
                _logger.LogError("Errors GetEmployeeShift", result);
                return BadRequest(result);
            }

        } 
        [HttpPost("DeletShift")]
        public async Task<IActionResult> DeletShift(int shiftId)
        {
            var result = await _ISlotsService.DeletShift(shiftId);
            if (result.State == true)
            {
                return Ok(result);
            }
            else
            {
                _logger.LogError("Errors GetEmployeeShift", result);
                return BadRequest(result);
            }

        }
    }
}

