using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Threading.Tasks;
using TecnoTech.BeautyCenter.DTOs.DTOModel;
using TecnoTech.BeautyCenter.Services.Services;

namespace TecnoTech.BeautyCenter.Interface.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class SettingsController : ControllerBase
    {
        private readonly ILogger<SettingsController> _logger;
        private readonly ISettingsService _SettingsService;
        private readonly IConfiguration _Configure;

        public SettingsController(ILogger<SettingsController> logger,
            ISettingsService SettingsService,
            IConfiguration configure)
        {
            _logger = logger;
            _SettingsService = SettingsService;
            _Configure = configure;

        }
        [HttpGet("IsAutoApprove")]
        public async Task<bool> IsAutoApprove()
        {
           var result =await _SettingsService.IsAutoAprrovable();
            return result ;
        }

        [HttpGet("AutoApprove")]
        public async Task<IActionResult> AutoApprove(bool State)
        {
            var result = await _SettingsService.AutoAprrovable(State);
            return result.State ? Ok(result) : BadRequest(result);
        }

        [HttpGet("GetAllShiftType")]
        public async Task<IActionResult> GetAllShiftType()
        {
            var result = await _SettingsService.GetAllShiftType();
            return result.State ?  Ok(result):BadRequest(result);
        }

        [HttpGet("GetAllShiftTypeById")]
        public async Task<IActionResult> GetAllShiftTypeById(int ShiftTypeId)
        {
            var result = await _SettingsService.GetAllShiftTypeById(ShiftTypeId);
            return result.State ? Ok(result) : BadRequest(result);
        }

        [HttpPost("AddShiftType")]
        public async Task<IActionResult> AddShiftType(AddShiftTypeDTO addShiftTypeDTO)
        {
            var result = await _SettingsService.AddShiftType(addShiftTypeDTO);
            return result.State ? Ok(result) : BadRequest(result);
        }

        [HttpPost("UpdateShiftType")]
        public async Task<IActionResult> UpdateShiftType(UpdateShiftTypeDTO shiftTypeDTO)
        {
            var result = await _SettingsService.UpdateShiftType(shiftTypeDTO);
            return result.State ? Ok(result) : BadRequest(result);
        }

        [HttpGet("DeleteShiftType")]
        public async Task<bool> DeleteShiftType(int shiftTypeId)
        {
            var result = await _SettingsService.DeleteShiftType(shiftTypeId);
            return result;
        }

    }
}
