using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using TecnoTech.BeautyCenter.DTOs.CoreDTO;
using TecnoTech.BeautyCenter.DTOs.DTOModel;
using TecnoTech.BeautyCenter.Services.Services;

namespace TecnoTech.BeautyCenter.Interface.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class BillController : ControllerBase
    {
        private readonly IBillService _BillService;

        public BillController(IBillService billService)
        {
            _BillService = billService;            
        }

        [HttpPost("PayBill")]
        public async Task<ResultDTO<BillDTO>> PayBill(PayBillDTO billDTO)
        {
            var result = await _BillService.PayBill(billDTO);
            return result;
        }
    }
}
