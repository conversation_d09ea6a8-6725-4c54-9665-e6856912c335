using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TecnoTech.BeautyCenter.DTOs.DTOModel;
using Microsoft.Extensions.Logging;
using TecnoTech.BeautyCenter.Services.Services;
using System.Net.Http;
using System.Net;
using Microsoft.Extensions.Configuration;
using System.Linq;

namespace TecnoTech.BeautyCenter.Interface.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class ServicesController : ControllerBase
    {

        private readonly ILogger<ServicesController> _logger;
        private readonly IServicesService _ServicesService;
        private readonly IConfiguration _Configure;

        public ServicesController(ILogger<ServicesController> logger,
            IServicesService ServicesService,
            IConfiguration configure)
        {
            _logger = logger;
            _ServicesService = ServicesService;
            _Configure = configure;

        }
        [HttpGet("GetServices")]
        public async Task<IActionResult> GetAll()
        {
            var res = await _ServicesService.GetAll();
            return Ok(res);
        }

        [HttpGet("GetServiceByName")]
        public async Task<IActionResult> GetServiceByName(string name)
        {
            _logger.LogError("Inputs GetServiceByName", name);

            var res = await _ServicesService.GetServiceByName(name);
            return Ok(res);
        }
        // GET api/<ValuesController>/5
        [HttpGet("{id}")]
        public async Task<IActionResult> GetAsync(int id)
        {
            _logger.LogError("Inputs GetAsync", id);
            var list = await _ServicesService.GetAsync(id);
            return Ok(list);
        }

        // POST api/<ValuesController>
        [HttpPost("Insert")]
        public async Task<IActionResult> Post([FromBody] ServicesDTO servicesDTO)
        {
            _logger.LogError("Inputs InsertService", servicesDTO);

            if (ModelState.IsValid)
            {
                var Result = await _ServicesService.Insert(servicesDTO);
                if (Result.State)
                {
                    return Ok(Result);
                }
                else
                {
                    _logger.LogError("Errors InsertService", Result);

                    return BadRequest(Result.Message);
                }
            }
            return BadRequest(ModelState);
        }

        // PUT api/<ValuesController>/5
        [HttpPut("Update")]
        public async Task<IActionResult> Put([FromBody] ServiceEditDTO servicesDTO)
        {
            _logger.LogError("Inputs UpdateService", servicesDTO);

            if (ModelState.IsValid)
            {
                var Result = await _ServicesService.Update(servicesDTO);
                if (Result.State)
                {
                    return Ok(Result);
                }
                else
                {
                    _logger.LogError("Errors UpdateService", servicesDTO);

                    return BadRequest(Result.Message);
                }
            }
            return BadRequest(ModelState);

        }

        [HttpPut("SignEmployeeInServices")]
        public async Task<IActionResult> SignEmployeeInServices(string EmployeeId, [FromBody] List<ServicesUserDTO> servicesList)
        {
            _logger.LogError("SignEmployeeInServices-EmployeeId", EmployeeId);
            _logger.LogError("Inputs SignEmployeeInServices", servicesList);

            if (ModelState.IsValid)
            {
                var Result = await _ServicesService.SignEmployeeInServices(EmployeeId, servicesList);
                if (Result.State)
                {
                    return Ok(Result);
                }
                else
                {
                    _logger.LogError("Errors SignEmployeeInServices", Result);
                    return BadRequest(Result.Message);
                }
            }
            return BadRequest(ModelState);
        }
        [HttpGet("GetEmployeeInServices")]
        public async Task<IActionResult> GetEmployeeInServices(string EmployeeId)
        {
            _logger.LogError("Inputs GetEmployeeInServices", EmployeeId);

            if (ModelState.IsValid)
            {
                var Result = await _ServicesService.GetEmployeeInServices(EmployeeId);
                if (Result.State)
                {
                    return Ok(Result);
                }
                else
                {
                    _logger.LogError("Errors GetEmployeeInServices", Result);

                    return BadRequest(Result.Message);
                }
            }
            return BadRequest(ModelState);
        }

        [HttpPost("UploadImage")]
        public async Task<IActionResult> UploadFile()
        {
            var resD = new Dictionary<string, dynamic>();
            var ReferenceKey = HttpContext.Request.Form["ReferenceKey"].ToString();
            var ReferencePath = HttpContext.Request.Form["ReferencePath"].ToString();
            var ReferenceId = HttpContext.Request.Form["ReferenceId"].ToString();
            var CreatedBy = HttpContext.Request.Form["CreatedBy"].ToString();
            var files = HttpContext.Request.Form.Files.ToList();
            var uploadDirecotroy = "";
            UploadFileDTO uploadFile = new UploadFileDTO()
            {
                files = files,
                ReferenceKey = ReferenceKey,
                ReferencePath = ReferencePath,
                ReferenceId = ReferenceId,
                CreatedBy = CreatedBy,
                uploadDirecotroy = uploadDirecotroy
            };
            _logger.LogError("Inputs UploadFile", uploadFile);
            var se = _ServicesService.UploadFile(uploadFile);
            var result = await se;
            if (result.State)
            {
                return Ok(result);
            }
            else
            {
                _logger.LogError("Errors UploadFile", result);

                return BadRequest(result.Message);
            }
        }


        [HttpGet("Display")]
        public async Task<IActionResult> Display(string name, string key)
        {
            _logger.LogError("name Display", name);
            _logger.LogError("key Display", key);

            var Result = await _ServicesService.Display(name, key);
            if (Result.State)
            {
                return File(Result.fileContents, Result.contentType);
            }
            else
            {
                _logger.LogError("Errors Display", Result);

                return BadRequest(Result.Message);
            }
        }

        // DELETE api/<ValuesController>/5
        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            try
            {
                _logger.LogError("inputs Delete", id);

                var Result = await _ServicesService.Delete(id);

                if (Result.State)
                {
                    return Ok(Result);
                }
                else
                {
                    _logger.LogError("Errors Delete", Result);

                    return BadRequest(Result.Message);
                }
            }
            catch (Exception ex)
            {

                ModelState.AddModelError("Errors", ex.Message);
                _logger.LogError("Errors Delete", ModelState);

                return BadRequest(ModelState);
            }

        }

        [HttpGet("GetBackgroundPhotoByKey")]
        public async Task<IActionResult> GetBackgroundPhotoByKey(string key)
        {
            try
            {
                _logger.LogError("inputs GetBackgroundPhotoByKey", key);

                var Result = await _ServicesService.GetBackgroundPhotoByKey(key);

                if (Result.State)
                {
                    return Ok(Result);
                }
                else
                {
                    _logger.LogError("Errors GetBackgroundPhotoByKey", Result);

                    return BadRequest(Result.Message);
                }
            }
            catch (Exception ex)
            {

                ModelState.AddModelError("Errors", ex.Message);
                _logger.LogError("Errors GetBackgroundPhotoByKey", ModelState);

                return BadRequest(ModelState);
            }
        }

        [HttpGet("GetBackgroundPhoto")]
        public async Task<IActionResult> GetBackgroundPhoto()
        {
            try
            {
                var Result = await _ServicesService.GetBackgroundPhoto();

                if (Result.State)
                {
                    return Ok(Result);
                }
                else
                {
                    _logger.LogError("Errors GetBackgroundPhoto", Result);

                    return BadRequest(Result.Message);
                }
            }
            catch (Exception ex)
            {

                ModelState.AddModelError("Errors", ex.Message);
                _logger.LogError("Errors GetBackgroundPhoto", ModelState);

                return BadRequest(ModelState);
            }
        }

        [HttpPost("SendNotification")]
        public HttpResponseMessage SendNotification(NotificationFireBaseDTO notificationData)
        {

            var entity = new SendNotification()
            {
                FirebaseToken = notificationData.FirebaseToken,
                title = notificationData.title,
                body = notificationData.body,
                image = notificationData.image,
                ServerKey = _Configure["firebase:ServerKey"],
                SenderId = _Configure["firebase:SenderId"]
            };
            _logger.LogError("Inputs SendNotification", entity);

            var result = _ServicesService.SendNotification(entity);
            var res = result.Result.Object.ContainsKey("Message").ToString();
            _logger.LogError("result SendNotification", result);

            return new HttpResponseMessage(HttpStatusCode.OK) { Content = new StringContent(res, System.Text.Encoding.UTF8, "application/json") };

        }

        [HttpGet("GetRefrenceBackgroundPhoto")]
        public async Task<IActionResult> GetRefrenceBackgroundPhoto()
        {
            try
            {
                var Result = await _ServicesService.GetRefrenceBackgroundPhoto();

                if (Result.State)
                {
                    return Ok(Result);
                }
                else
                {
                    _logger.LogError("Errors GetBackgroundPhoto", Result);

                    return BadRequest(Result.Message);
                }
            }
            catch (Exception ex)
            {

                ModelState.AddModelError("Errors", ex.Message);
                _logger.LogError("Errors GetBackgroundPhoto", ModelState);

                return BadRequest(ModelState);
            }
        }

    }
}

