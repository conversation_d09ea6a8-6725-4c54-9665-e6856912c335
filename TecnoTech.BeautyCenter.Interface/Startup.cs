using AutoMapper;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.HttpsPolicy;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.OpenApi.Models;
using Newtonsoft.Json.Serialization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TecnoTech.BeautyCenter.DAL.SqlServer.Context;
using TecnoTech.BeautyCenter.DAL.SqlServer.Models;
using TecnoTech.BeautyCenter.DTOs.Configuration;
using TecnoTech.BeautyCenter.DTOs.DTOModel;
using TecnoTech.BeautyCenter.Repository.Abstraction;
using TecnoTech.BeautyCenter.Services.Abstraction;
using TecnoTech.BeautyCenter.Services.Services;
using TecnoTech.BeautyCenter.UnitOfWork.unitOfWork;

namespace TecnoTech.BeautyCenter.Interface
{
    public class Startup
    {
        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
        }

        public IConfiguration Configuration { get; }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            //var MyAllowSpecificOrigins = "_myAllowSpecificOrigins";

            //services.AddCors(options =>
            //{
            //    options.AddPolicy(name: MyAllowSpecificOrigins,
            //                      policy =>
            //                      {
            //                          policy.WithOrigins("https://localhost:44320/",
            //                                              "http://************:90/");
            //                      });
            //});
            //services.AddCors(); // Make sure you call this previous to AddMvc
            services.AddSignalR();
            services.AddCors(options => options.AddPolicy("ApiCorsPolicy", builder =>
            {
                builder.WithOrigins("http://localhost:4200").AllowAnyMethod().AllowAnyHeader();
            }));
            services.AddMvc();

            services.AddDbContext<BeautyCenterContext>(options =>
                options.UseSqlServer(
                    Configuration.GetConnectionString("DefaultConnection")));
            services.AddDatabaseDeveloperPageExceptionFilter();
            services.Configure<JwtConfigDTO>(Configuration.GetSection("JwtConfig"));
            services.AddDefaultIdentity<ApplicationUser>(options => options.SignIn.RequireConfirmedAccount = true)
                .AddRoles<IdentityRole>()
                .AddEntityFrameworkStores<BeautyCenterContext>();
            services.AddControllersWithViews();            
            services.AddScoped<IUnitOfWork,unitOfWork>();
            services.AddScoped<ISlotsService, SlotsService>();
            services.AddScoped<ITimelineService, TimelineService>();
            services.AddScoped<IEmployeesService, EmployeesService>();
            services.AddScoped<IGeneralService, GeneralService>();
            services.AddScoped<IAccountentService, AccountentService>();
            services.AddScoped<IServicesService, ServicesService>();
            services.AddScoped<IBillService, BillService>();
            services.AddScoped<IAppointmentService, AppointmentService>();
            services.AddScoped<ICustomerService, CustomerService>();
            services.AddScoped<ISettingsService, SettingsService>();
            services.AddScoped<INotificationService, NotificationService>();
            services.AddControllersWithViews().AddNewtonsoftJson(options =>
            options.SerializerSettings.ReferenceLoopHandling = Newtonsoft.Json.ReferenceLoopHandling.Ignore)
               .AddNewtonsoftJson(options => options.SerializerSettings.ContractResolver
               = new DefaultContractResolver());
            services.AddCors(c =>
            {
                c.AddPolicy("AllowOrigin", options => options.AllowAnyOrigin().AllowAnyMethod().AllowAnyHeader());
            });
            services.AddTransient<ITokenService, TokenService>();
            services.AddSwaggerGen(c =>
            {
                c.SwaggerDoc("v1", new OpenApiInfo { Title = "WebApplication1", Version = "v1" });
            });
        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
        {
            //var MyAllowSpecificOrigins = "_myAllowSpecificOrigins";

            if (env.IsDevelopment())
            {

                app.UseDeveloperExceptionPage();
                app.UseMigrationsEndPoint();
                app.UseSwagger();
                //app.UseSwaggerUI(c => c.SwaggerEndpoint("/swagger/v1/swagger.json", "WebApplication1 v1"));
            }
            else
            {
                app.UseExceptionHandler("/Home/Error");
                // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
                app.UseHsts();
            }
            app.UseSwagger();
            app.UseSwaggerUI(c => c.SwaggerEndpoint("/swagger/v1/swagger.json", "WebApplication1 v1"));
            app.UseHttpsRedirection();
            app.UseStaticFiles();
            //app.UseCors(MyAllowSpecificOrigins);
            //          app.UseCors(
            //    options => options.WithOrigins("http://************:90/").AllowAnyMethod()
            //);
            app.UseCors(builder => builder
              .AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader());
            //app.UseMvc();
            app.UseRouting();

            app.UseAuthentication();
            app.UseAuthorization();
            app.UseSwaggerUI(options =>
            {
                options.SwaggerEndpoint("/swagger/v1/swagger.json", "v1");
                options.RoutePrefix = string.Empty;
            });
            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllerRoute(
                    name: "default",
                    pattern: "{controller=Home}/{action=Index}/{id?}");
                endpoints.MapRazorPages();
            });
            app.UseEndpoints(endpoints =>
            {
                endpoints.MapHub<NotificationHub>("/api/NotificationHub");
            });
        }
    }
}
