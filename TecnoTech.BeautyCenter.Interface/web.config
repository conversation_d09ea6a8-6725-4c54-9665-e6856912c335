<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <location path="." inheritInChildApplications="false">
    <system.webServer>
      <handlers>
        <add name="aspNetCore" path="*" verb="*" modules="AspNetCoreModuleV2" resourceType="Unspecified" />
      </handlers>
      <aspNetCore processPath="dotnet" arguments=".\TecnoTech.BeautyCenter.Interface.dll" stdoutLogEnabled="false" stdoutLogFile=".\logs\stdout" hostingModel="inprocess" />
       <modules runAllManagedModulesForAllRequests="false">
            <remove name="WebDAVModule" />
          </modules>
   </system.webServer>
  </location>
</configuration>
<!--ProjectGuid: 38BBA635-F2A8-40A6-90DB-59D2692993B2-->