<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net5.0</TargetFramework>
    <UserSecretsId>aspnet-TecnoTech.BeautyCenter.Interface-0DB16AFF-F8BA-4DAA-83FA-B0F910689DC0</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Areas\**" />
    <Compile Remove="Service\**" />
    <Compile Remove="ViewComponents\**" />
    <Compile Remove="Views\**" />
    <Content Remove="Areas\**" />
    <Content Remove="Service\**" />
    <Content Remove="ViewComponents\**" />
    <Content Remove="Views\**" />
    <EmbeddedResource Remove="Areas\**" />
    <EmbeddedResource Remove="Service\**" />
    <EmbeddedResource Remove="ViewComponents\**" />
    <EmbeddedResource Remove="Views\**" />
    <None Remove="Areas\**" />
    <None Remove="Service\**" />
    <None Remove="ViewComponents\**" />
    <None Remove="Views\**" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="Controllers\AppointmentsController.cs~RF16ed750.TMP" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="11.0.1" />
    <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="11.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Cors" Version="2.2.0" />
    <PackageReference Include="Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore" Version="5.0.17" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="5.0.17" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.UI" Version="5.0.17" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="5.0.17" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="5.0.17" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="5.0.17">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Design" Version="5.0.2" />
    <PackageReference Include="Nancy" Version="2.0.0" />
    <PackageReference Include="Serilog.AspNetCore" Version="4.0.0" />
    <PackageReference Include="Serilog.Settings.Configuration" Version="3.1.0" />
    <PackageReference Include="Serilog.Sinks.MSSqlServer" Version="5.6.0" />
    <PackageReference Include="SixLabors.ImageSharp" Version="2.1.2" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.3.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\TecnoTech.BeautyCenter.DAL.SqlServer\TecnoTech.BeautyCenter.DAL.SqlServer.csproj" />
    <ProjectReference Include="..\TecnoTech.BeautyCenter.DTOs\TecnoTech.BeautyCenter.DTOs.csproj" />
    <ProjectReference Include="..\TecnoTech.BeautyCenter.Services\TecnoTech.BeautyCenter.Services.csproj" />
    <ProjectReference Include="..\TecnoTech.BeautyCenter.UnitOfWork\TecnoTech.BeautyCenter.UnitOfWork.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Include="wwwroot\js\site.js" />
    <None Include="wwwroot\lib\bootstrap\dist\css\bootstrap-grid.css.map" />
    <None Include="wwwroot\lib\bootstrap\dist\css\bootstrap-grid.min.css.map" />
    <None Include="wwwroot\lib\bootstrap\dist\css\bootstrap-reboot.css.map" />
    <None Include="wwwroot\lib\bootstrap\dist\css\bootstrap-reboot.min.css.map" />
    <None Include="wwwroot\lib\bootstrap\dist\css\bootstrap.css.map" />
    <None Include="wwwroot\lib\bootstrap\dist\css\bootstrap.min.css.map" />
    <None Include="wwwroot\lib\bootstrap\dist\js\bootstrap.bundle.js" />
    <None Include="wwwroot\lib\bootstrap\dist\js\bootstrap.bundle.js.map" />
    <None Include="wwwroot\lib\bootstrap\dist\js\bootstrap.bundle.min.js" />
    <None Include="wwwroot\lib\bootstrap\dist\js\bootstrap.bundle.min.js.map" />
    <None Include="wwwroot\lib\bootstrap\dist\js\bootstrap.js" />
    <None Include="wwwroot\lib\bootstrap\dist\js\bootstrap.js.map" />
    <None Include="wwwroot\lib\bootstrap\dist\js\bootstrap.min.js" />
    <None Include="wwwroot\lib\bootstrap\dist\js\bootstrap.min.js.map" />
    <None Include="wwwroot\lib\bootstrap\LICENSE" />
    <None Include="wwwroot\lib\jquery-validation-unobtrusive\jquery.validate.unobtrusive.js" />
    <None Include="wwwroot\lib\jquery-validation-unobtrusive\jquery.validate.unobtrusive.min.js" />
    <None Include="wwwroot\lib\jquery-validation\dist\additional-methods.js" />
    <None Include="wwwroot\lib\jquery-validation\dist\additional-methods.min.js" />
    <None Include="wwwroot\lib\jquery-validation\dist\jquery.validate.js" />
    <None Include="wwwroot\lib\jquery-validation\dist\jquery.validate.min.js" />
    <None Include="wwwroot\lib\jquery-validation\LICENSE.md" />
    <None Include="wwwroot\lib\jquery\dist\jquery.js" />
    <None Include="wwwroot\lib\jquery\dist\jquery.min.js" />
    <None Include="wwwroot\lib\jquery\dist\jquery.min.map" />
  </ItemGroup>

</Project>
