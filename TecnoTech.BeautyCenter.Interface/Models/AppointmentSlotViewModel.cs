using System;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json.Serialization;
using TecnoTech.BeautyCenter.DAL.SqlServer.Models;

namespace TecnoTech.BeautyCenter.Interface.Models
{
    public class AppointmentSlotViewModel
    {
        public int Id { get; set; }
        public DateTime Start { get; set; }
        public DateTime End { get; set; }

        [JsonPropertyName("text")]
        public string? UserName { set; get; }

        [JsonPropertyName("User")]
        public string? UserId { set; get; }

        public string Status { get; set; } = "free";

        [NotMapped]
        public string EmployeeId { get; set; }

        [NotMapped]
        public string EmployeeName { get; set; }
    }
}
