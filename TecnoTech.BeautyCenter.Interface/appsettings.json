{
  "ConnectionStrings": {
    "DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=aspnet-TecnoTech.BeautyCenter.Interface-0DB16AFF-F8BA-4DAA-83FA-B0F910689DC0;Trusted_Connection=True;MultipleActiveResultSets=true",
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  },
  "firebase": {
    "ServerKey": "",
    "SenderId": ""
  },
  "URL": {
    "Base": "http://**************:98/",
    "DisplayImage": "http://**************:98/api/Services/Display?name=",
    "BGPhoto": "C:/uploads/BackgroundPhoto/",
    "IconService": "C:/uploads/ServicesIcons/",
    "ImageEmp": "C:/uploads/EmployeesImage",
    "ImageNotification": "C:/uploads/ImageNotification"
  },
  //"path": {
  //  "pathBackground": "C:/uploads/BackgroundPhoto/",
  //  "pathServiceIcon": "C:/uploads/ServicesIcons/",
  //  "pathEmployeesImage": "C:/uploads/EmployeesImage"
  //}
  //"JwtConfig": {
  //  "Secret": "ijurkbdlhmklqacwqzdxmkkhvqowlyqa"
  //},
  "Jwt": {
    "Key": "ThisismySecretKey",
    "Issuer": "www.joydipkanjilal.net",
    "Audience": "http://localhost:36145/"
  },
  "AllowedHosts": "*",
  "Serilog": {
    "MinimumLevel": "Warning",
    "WriteTo": [

      {
        "Name": "Console"
      },
      {
        "Name": "MSSqlServer",
        "Args": {
          "connectionString": "Server=(localdb)\\mssqllocaldb;Database=BeautyCenterDB;User Id=sa;Password=;",
          "tableName": "Logs",
          "autoCreateSqlTable": true
        }
      }
    ]
  }
}
