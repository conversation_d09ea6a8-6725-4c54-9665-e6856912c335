using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TecnoTech.BeautyCenter.DAL.SqlServer.Context;
using TecnoTech.BeautyCenter.Repository.Abstraction;

namespace TecnoTech.BeautyCenter.Repository.Concert
{
    public class Repository<TEtity> : IRepository<TEtity> where TEtity : class
    {
        protected BeautyCenterContext _BeautyCenterContext { get; set; }
        public Repository(BeautyCenterContext BeautyCenterContext)
        {
            this._BeautyCenterContext = BeautyCenterContext;

        }
        public TEtity Add(TEtity entity)
        {
            _BeautyCenterContext.Set<TEtity>().Add(entity);
            return _BeautyCenterContext.SaveChanges() > 0 ? entity : null;
        }
        public bool AddReng(List<TEtity> entity)
        {
            _BeautyCenterContext.Set<TEtity>().AddRange(entity);
            return _BeautyCenterContext.SaveChanges() > 0 ;
        }
        public async Task<bool> AddRengAsync(List<TEtity> entity)
        {
            _BeautyCenterContext.Set<TEtity>().AddRange(entity);
            return await _BeautyCenterContext.SaveChangesAsync() > 0 ;
        }
        public bool RemoveRange(List<TEtity> entity)
        {
            _BeautyCenterContext.Set<TEtity>().RemoveRange(entity);
            return _BeautyCenterContext.SaveChanges() > 0 ;
        }
        public async Task<bool>RemoveRangeAsync(List<TEtity> entity)
        {
            _BeautyCenterContext.Set<TEtity>().RemoveRange(entity);
            return await _BeautyCenterContext.SaveChangesAsync() > 0 ;
        }
        public async Task<TEtity> AddAsync(TEtity entity)
        {
            var result = await _BeautyCenterContext.Set<TEtity>().AddAsync(entity);
            return await _BeautyCenterContext.SaveChangesAsync() > 0 ? entity : null;
        }
        public TEtity GetBy(params object[] Id)
        {
            var MyObject = _BeautyCenterContext.Set<TEtity>().Find(Id);
            return MyObject;
        }
        public async Task<TEtity> GetByAsync(params object[] Id)
        {
            //var MyObject = await _BeautyCenterContext.Set<TEtity>().FindAsync(Id);
            return await _BeautyCenterContext.Set<TEtity>().FindAsync(Id);
        }
        public List<TEtity> GetAllBind()
        {
            return GetAll().ToList();
        }
        public IEnumerable<TEtity> GetAll()
        {
            return this._BeautyCenterContext.Set<TEtity>().AsNoTracking();
        }
        public async Task<IEnumerable<TEtity>> GetAllAsyn()
        {
            return await this._BeautyCenterContext.Set<TEtity>().ToListAsync(); ;
        }
        public IQueryable<TEtity> GetAllQurAsync()
        {
            return this._BeautyCenterContext.Set<TEtity>();
        }
        public TEtity Remove(params object[] Id)
        {
            var entity = GetBy(Id);
            _BeautyCenterContext.Set<TEtity>().Remove(GetBy(Id));

            return _BeautyCenterContext.SaveChanges() > 0 ? entity : null;
        }
        public TEtity Remove(TEtity entity)
        {
            _BeautyCenterContext.Set<TEtity>().Remove(entity);

            return _BeautyCenterContext.SaveChanges() > 0 ? entity : null;
        }
        public async Task<TEtity> RemoveAsync(params object[] Id)
        {
            var entity = await _BeautyCenterContext.Set<TEtity>().FindAsync(Id);
            _BeautyCenterContext.Set<TEtity>().Remove(entity);

            return await _BeautyCenterContext.SaveChangesAsync() > 0 ? entity : null;
        }
        public bool Update(TEtity entity)
        {
            _BeautyCenterContext.Set<TEtity>().Update(entity);
            //_BeautyCenterContext.Set<TEtity>().Entry(entity).State = EntityState.Modified;
            return _BeautyCenterContext.SaveChanges() > 0 ;
        }
        public async Task<bool> UpdateAsync(TEtity entity)
        {
            _BeautyCenterContext.Set<TEtity>().Update(entity);
            //_BeautyCenterContext.Set<TEtity>().Entry(entity).State = EntityState.Modified;
            return await _BeautyCenterContext.SaveChangesAsync() > 0 ;
        }
        public void Save()
        {
            _BeautyCenterContext.SaveChanges();
        }


    }
}

