using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TecnoTech.BeautyCenter.Repository.Abstraction
{
    public interface IRepository<TEntity>
    {
        IEnumerable<TEntity> GetAll();
        IQueryable<TEntity> GetAllQurAsync();
        List<TEntity> GetAllBind();
        TEntity Add(TEntity entity);
        Task<TEntity> AddAsync(TEntity entity);
        TEntity GetBy(params object[] Id);
        Task<TEntity> GetByAsync(params object[] Id);
        bool Update(TEntity entity);
        Task<bool> UpdateAsync(TEntity entity);
        TEntity Remove(TEntity entity);
        Task<TEntity> RemoveAsync(params object[] Id);
        TEntity Remove(params object[] Id);
        Task<bool> RemoveRangeAsync(List<TEntity> entity);
        bool RemoveRange(List<TEntity> entity);
        Task<bool> AddRengAsync(List<TEntity> entity);
        bool AddReng(List<TEntity> entity);
    }
}
