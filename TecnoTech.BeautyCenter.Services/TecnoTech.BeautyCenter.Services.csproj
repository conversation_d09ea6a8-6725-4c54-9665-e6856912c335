<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net5.0</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Cryptography.KeyDerivation" Version="6.0.8" />
    <PackageReference Include="Microsoft.AspNetCore.Identity" Version="2.2.0" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="5.0.17" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.UI" Version="5.0.17" />
    <PackageReference Include="Microsoft.IdentityModel.Tokens" Version="6.18.0" />
    <PackageReference Include="Nancy" Version="2.0.0" />
    <PackageReference Include="Security.Cryptography" Version="1.7.2" />
    <PackageReference Include="SixLabors.ImageSharp" Version="2.1.2" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="6.18.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\TecnoTech.BeautyCenter.DAL.SqlServer\TecnoTech.BeautyCenter.DAL.SqlServer.csproj" />
    <ProjectReference Include="..\TecnoTech.BeautyCenter.DTOs\TecnoTech.BeautyCenter.DTOs.csproj" />
    <ProjectReference Include="..\TecnoTech.BeautyCenter.UnitOfWork\TecnoTech.BeautyCenter.UnitOfWork.csproj" />
  </ItemGroup>

</Project>
