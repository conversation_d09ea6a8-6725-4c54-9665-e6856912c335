using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TecnoTech.BeautyCenter.DAL.SqlServer.Models;
using TecnoTech.BeautyCenter.DTOs.CoreDTO;
using TecnoTech.BeautyCenter.DTOs.DTOModel;

namespace TecnoTech.BeautyCenter.Services.Services
{
    public interface ISlotsService
    {
        Task<ResultDTO<Dictionary<string, dynamic>>> GetAllAvailableSlots(AppointmentsRequestDTO requestDTO);
        Task<ResultDTO<Dictionary<string, dynamic>>> CreateShift(List<RequestShiftDTO> requestShiftList);
        Task<ResultDTO<Dictionary<string, dynamic>>> CreateShift(LongShiftDTO longShiftDT);
        Task<Slot[]> checkIfShiftInUse(Shift Shift);
        Task<bool> checkIfShiftInUse(int ShiftId);
        Task<bool> CheckIfShiftIsExcist(checkIfShiftIsExcistDTO checkIfShiftIs);
        Task<ResultfShiftStateDTO> ResultfShiftState(checkIfShiftIsExcistDTO checkIf);
        Task<ResultDTO<Dictionary<string, dynamic>>> DeletShift(int ShiftId);
    }
}
