using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;
using TecnoTech.BeautyCenter.DAL.SqlServer.EnumsModel;
using TecnoTech.BeautyCenter.DAL.SqlServer.Models;
using TecnoTech.BeautyCenter.DTOs.Administration;
using TecnoTech.BeautyCenter.DTOs.Configuration;
using TecnoTech.BeautyCenter.DTOs.CoreDTO;
using TecnoTech.BeautyCenter.DTOs.DTOModel;
using TecnoTech.BeautyCenter.DTOs.DTOModel.Requests.FromMobile;
using TecnoTech.BeautyCenter.DTOs.DTOModel.Response;
using TecnoTech.BeautyCenter.UnitOfWork.unitOfWork;
using Microsoft.AspNetCore.Cryptography.KeyDerivation;
using System.Security.Cryptography;


namespace TecnoTech.BeautyCenter.Services.Services
{
    public class AccountentService : IAccountentService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly RoleManager<IdentityRole> roleManager;
        private SignInManager<ApplicationUser> signInManager;
        private readonly IConfiguration _configuration;
        private readonly JwtConfigDTO _jwtConfig;
        private readonly ILogger<AccountentService> _logger;

        public AccountentService(IUnitOfWork unitOfWork,
                                  IOptionsMonitor<JwtConfigDTO> optionsMonitor,
                                  UserManager<ApplicationUser> userManager,
                                  ILogger<AccountentService> logger,
                                  RoleManager<IdentityRole> roleManager = null,
                                  IConfiguration configuration = null,
                                  SignInManager<ApplicationUser> signInManager = null,
                                  JwtConfigDTO jwtConfig = null
                                  )
        {
            _logger = logger;
            this._userManager = userManager;
            this.roleManager = roleManager;
            _configuration = configuration;
            this.signInManager = signInManager;
            _jwtConfig = optionsMonitor.CurrentValue;
            _unitOfWork = unitOfWork;

        }
        public async Task<RegistrationResponseDTO> Register(RegistrationRequestDTO user)
        {
            //string FirstComp = "test";
            //string ScoundComp = "@test.com";
            string PasswordComp = "123456aA@";
            //var existingUser = await _userManager.FindByEmailAsync(FirstComp + user.Mobile + ScoundComp);
            var existingUser = _unitOfWork.applicationUserManger.GetAllQurAsync().Where(r => r.PhoneNumber == user.Mobile && r.isDeleted ==false).FirstOrDefault();

            if (existingUser != null)
            {
                string ErrorMessage = "this account already exist - use  another  phone  number ";
                _logger.LogWarning(ErrorMessage);
                return new RegistrationResponseDTO()
                {
                    Result = false,
                    Errors = new List<string>()
                    {
                            ErrorMessage
                    },
                };
            }
            var existingFingerPrint = _unitOfWork.applicationUserManger.GetAllQurAsync().Where(r=>r.fingerPrintIdAndroid==user.fingerPrintIdAndroid&&r.isDeleted == false).FirstOrDefault();
            if (existingFingerPrint != null)
            {
                string ErrorMessage = "this account already exist - use  another finger Print ";
                _logger.LogWarning(ErrorMessage);
                return new RegistrationResponseDTO()
                {
                    Result = false,
                    Errors = new List<string>()
                    {
                            ErrorMessage
                    },
                };
            }
            var newUser = new ApplicationUser()
            {
                ExternalEmail = user.Email,
                PhoneNumber = user.Mobile,
                UserName = user.Name,
                FirstNameEn = user.FirstName.Trim(),
                lastNameEn = user.LastName.Trim(),
                //Email = "test" + user.Mobile + "@test.com",
                Email = user.Email==""? "test" + user.Mobile +"@test.com": user.Email == null ? "test" + user.Mobile + "@test.com" : user.Email,
                UserType = (int)AllEnumes.UserType.Customer,
                device_token=user.device_token,
                isDeleted =false,
                Image = _configuration["URL:Base"] + "api/Services/Display?name=user-Default-User.png&key=EmployeesImage",
                fingerPrintIdAndroid=user.fingerPrintIdAndroid==""? null:user.fingerPrintIdAndroid
            };
            var CreateCustomePass = user.Password + PasswordComp;
            var isCreated = await _userManager.CreateAsync(newUser, CreateCustomePass);

            if (isCreated.Succeeded)
            {
                ApplicationUser myUser = await _userManager.FindByEmailAsync(newUser.Email);
                var rolename = await _userManager.GetRolesAsync(myUser);
                var jwtToken = GenerateJwtToken(newUser);
                _logger.LogWarning("an account Is Created ");
                return new RegistrationResponseDTO()
                {
                    Result = true,
                    Token = jwtToken,
                    Id = myUser.Id,
                };
            }

            return new RegistrationResponseDTO()
            {
                Result = false,
                Errors = isCreated.Errors.Select(x => x.Description).ToList()
            };
        }
        private string GenerateJwtToken(IdentityUser user)
        {
            try
            {
                // Now its ime to define the jwt token which will be responsible of creating our tokens
                var jwtTokenHandler = new JwtSecurityTokenHandler();

                // We get our secret from the appsettings
                var key = Encoding.ASCII.GetBytes("ijurkbdlhmklqacwqzdxmkkhvqowlyqa");
                var tokenDescriptor = new SecurityTokenDescriptor() { };
                // we define our token descriptor
                // We need to utilise claims which are properties in our token which gives information about the token
                // which belong to the specific user who it belongs to
                // so it could contain their id, name, email the good part is that these information
                // are generated by our server and identity framework which is valid and trusted
                      var existingUser = _unitOfWork.applicationUserManger.GetAllQurAsync().Where(r=>r.PhoneNumber==user.PhoneNumber&&r.isDeleted == false).FirstOrDefault();
                if (existingUser != null || existingUser.isDeleted != true)
                {
                 tokenDescriptor = new SecurityTokenDescriptor
                {
                    Subject = new ClaimsIdentity(new[]
                    {
                        new Claim("Id", user.Id),
                        new Claim("Img", existingUser.Image==null?"":existingUser.Image),
                        new Claim("Name", existingUser.FirstNameEn +" "+existingUser.lastNameEn),
                        new Claim(JwtRegisteredClaimNames.Sub, user.Email),
                        new Claim(JwtRegisteredClaimNames.Email, user.Email),
                        // the JTI is used for our refresh token which we will be convering in the next video
                        new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString())
                    }),
                    // the life span of the token needs to be shorter and utilise refresh token to keep the user signedin
                    // but since this is a demo app we can extend it to fit our current need
                    Expires = DateTime.UtcNow.AddHours(6),
                    // here we are adding the encryption alogorithim information which will be used to decrypt our token
                    SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha512Signature)
                };
                }
                else
                {
                     tokenDescriptor = new SecurityTokenDescriptor
                    {
                        Subject = new ClaimsIdentity(new[]
    {
                        new Claim("Id", user.Id),
                        new Claim("Img", ""),
                        new Claim("Name", user.UserName),
                        new Claim(JwtRegisteredClaimNames.Sub, user.Email),
                        new Claim(JwtRegisteredClaimNames.Email, user.Email),
                        // the JTI is used for our refresh token which we will be convering in the next video
                        new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString())
                    }),
                        // the life span of the token needs to be shorter and utilise refresh token to keep the user signedin
                        // but since this is a demo app we can extend it to fit our current need
                        Expires = DateTime.UtcNow.AddHours(6),
                        // here we are adding the encryption alogorithim information which will be used to decrypt our token
                        SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha512Signature)
                    };

                }
                var token = jwtTokenHandler.CreateToken(tokenDescriptor);
                var jwtToken = jwtTokenHandler.WriteToken(token);
                return jwtToken;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex.Message);
                throw;
            }
        }

        public async Task<RegistrationResponseDTO> ResetPassword(ResetPassword user)
        {
            var existingMobileUser = _unitOfWork.applicationUserManger.GetAllQurAsync().Where(r => r.PhoneNumber == user.mobile).FirstOrDefault();

            if (existingMobileUser == null)
            {
    
                    string ErrorMessage = "This mobile Not exist - Can't Reset Password ! ";
                
                _logger.LogWarning(ErrorMessage);
                return new RegistrationResponseDTO()
                {
                    Result = false,
                    Errors = new List<string>()
                    {
                            ErrorMessage
                    },
                };
            }
            if (existingMobileUser == null)
            {

                string ErrorMessage = "This account is not have username - the password cannot be reset!";

                _logger.LogWarning(ErrorMessage);
                return new RegistrationResponseDTO()
                {
                    Result = false,
                    Errors = new List<string>()
                    {
                            ErrorMessage
                    },
                };
            }
            var existingUser = _unitOfWork.applicationUserManger.GetAllQurAsync().Where(r => r.PhoneNumber == user.mobile && r.UserName == user.username).FirstOrDefault();

            if (existingUser == null)
            {

                string ErrorMessage = "This mobile is not associated with this username - the password cannot be reset!";

                _logger.LogWarning(ErrorMessage);
                return new RegistrationResponseDTO()
                {
                    Result = false,
                    Errors = new List<string>()
                    {
                            ErrorMessage
                    },
                };
            }

            //if (existingUser.fingerPrintIdAndroid!=null)
            //{
            //    var fingerUser = _unitOfWork.applicationUserManger.GetAllQurAsync().Where(r => r.fingerPrintIdAndroid == user.deviceId).FirstOrDefault();
            //    if (fingerUser == null)
            //    {
            //        //string ErrorMessage = "this account Contains another Finger Device - Can't Reset Password ! ";
            //        string ErrorMessage = "Can't Reset Password ! ";
            //        _logger.LogWarning(ErrorMessage);
            //        return new RegistrationResponseDTO()
            //        {
            //            Result = false,
            //            Errors = new List<string>()
            //        {
            //                ErrorMessage
            //        },
            //        };
            //    }
            //}
            //else
            //{
            //    existingUser.fingerPrintIdAndroid = user.deviceId;   
            //}
            if (user.password != user.confirmpassword)
            {
                string ErrorMessage = "Check Password Again ! The password does not match the confirmed password - Can't Reset Password ! ";
                _logger.LogWarning(ErrorMessage);
                return new RegistrationResponseDTO()
                {
                    Result = false,
                    Errors = new List<string>()
                    {
                            ErrorMessage
                    },
                };
            }
            // Generate a 128-bit salt using a sequence of
            // cryptographically strong random bytes.
            //byte[] salt = new byte[128 / 8];
            //using (var rng = RandomNumberGenerator.Create())
            //{
            //    rng.GetBytes(salt);
            //}
            //string hashedPass = Convert.ToBase64String(KeyDerivation.Pbkdf2(
            //password: user.password!,
            //salt: salt,
            //prf: KeyDerivationPrf.HMACSHA1,
            //iterationCount: 100000,
            //numBytesRequested: 256 / 8));
            //existingUser.PasswordHash = hashedPass;
            existingUser.isDeleted = false;
            //var CurrentUser = new ApplicationUser()
            //{
            //    ExternalEmail = existingUser.Email,
            //    Id = existingUser.Id,   
            //    PhoneNumber = user.mobile,
            //    UserName = existingUser.UserName,
            //    FirstNameEn = existingUser.FirstNameEn,
            //    lastNameEn = existingUser.lastNameEn,
            //    //Email = "test" + user.Mobile + "@test.com",
            //    Email = existingUser.Email ,
            //    UserType = existingUser.UserType,
            //    device_token = existingUser.device_token,
            //    isDeleted = false,
            //    Image = existingUser.Image,
            //    fingerPrintIdAndroid = existingUser.fingerPrintIdAndroid,
            //    PasswordHash= hashedPass
            //};
            string PasswordComp = "123456aA@";
            var CreateCustomePass = user.password + PasswordComp;
            var passwordHasher = new PasswordHasher<string>();
            var hashedPassword = passwordHasher.HashPassword(null, CreateCustomePass);
            //var isUpdated = await _userManager.ChangePasswordAsync(existingUser, currpass, hashedPassword);
            
            existingUser.PasswordHash= hashedPassword;

            var isUpdated = await _userManager.UpdateAsync(existingUser);
            if (isUpdated.Succeeded)
            {
                ApplicationUser myUser = await _userManager.FindByEmailAsync(existingUser.Email);
                var rolename = await _userManager.GetRolesAsync(myUser);
                var jwtToken = GenerateJwtToken(existingUser);
                _logger.LogWarning("an account Is Updated ");
                return new RegistrationResponseDTO()
                {
                    Result = true,
                    Token = jwtToken,
                    Id = myUser.Id,
                };
            }

            return new RegistrationResponseDTO()
            {
                Result = false,
                Errors = isUpdated.Errors.Select(x => x.Description).ToList()
            };
        }

        public async Task Logout()
        {
            await this.signInManager.SignOutAsync();
        }

        public async Task<RegistrationResponseDTO> Login(LoginRequestDTO user)
        {
            var resultAdd = await addAdmin();
            // check if the user with the same email exist
            //string ErrorMessage = "Invalid authentication request";
            string ErrorMessage = "User Not Exist";

            //string FirstComp = "test";
            //string ScoundComp = "@test.com";
            string PasswordComp = "123456aA@";

            //var existingUser = await _userManager.FindByEmailAsync(FirstComp + user.Mobile + ScoundComp);
            var existingUser = _unitOfWork.applicationUserManger.GetAllQurAsync().Where(r=>r.PhoneNumber==user.Mobile&&r.isDeleted == false).FirstOrDefault();
            if (existingUser == null || existingUser.isDeleted==true)
            {
                // We dont want to give to much information on why the request has failed for security reasons
                _logger.LogWarning(ErrorMessage);
                return new RegistrationResponseDTO()
                {
                    Result = false,
                    Errors = new List<string>(){
                            ErrorMessage
                        }
                };
            }
            //Can't moved this Befor Condation
            var rolename = await _userManager.GetRolesAsync(existingUser);
            existingUser.device_token = user.device_token;
            await _userManager.UpdateAsync(existingUser);
            //users = await _userManager.Users.u(r => r.isDeleted != true).ToListAsync();
            var CreateCustomePass = user.Password + PasswordComp;

            if (existingUser.Email== "<EMAIL>"|| existingUser.Email == "<EMAIL>")
            {
                CreateCustomePass = user.Password;
            }
            
            // Now we need to check if the user has inputed the right password
            var isCorrect = await _userManager.CheckPasswordAsync(existingUser, CreateCustomePass);
            if (isCorrect)
            {
                var jwtToken = GenerateJwtToken(existingUser);
                _logger.LogInformation("Logined");
                return new RegistrationResponseDTO()
                {
                    Result = true,
                    Token = jwtToken,
                    Id = existingUser.Id,
                    RolesName = rolename.ToList(),
                    UserName = existingUser.UserName
                };
            }
            else
            {
                // We dont want to give to much information on why the request has failed for security reasons
                _logger.LogWarning("the request has failed for security reasons");
                return new RegistrationResponseDTO()
                {
                    Result = false,
                    Errors = new List<string>(){
                            "User Not Exist"
                        }
                };
            }

        }
        public async Task<RegistrationResponseDTO> LoginFingerPrint(LoginFingerPrint user)
        {
            var resultAdd = await addAdmin();
            // check if the user with the same email exist
            var existingUser = _userManager.Users.Where(r => r.fingerPrintIdAndroid == user.fingerPrintIdAndroid && r.isDeleted == false).FirstOrDefault();
            if (existingUser == null)
            {
                // We dont want to give to much information on why the request has failed for security reasons
                _logger.LogWarning("Finger Print Not Exist");
                return new RegistrationResponseDTO()
                {
                    Result = false,
                    Errors = new List<string>(){
                            "Finger Print Not Exist"
                        }
                };
            }
            //Can't moved this Befor Condation
            var rolename = await _userManager.GetRolesAsync(existingUser);
            existingUser.device_token = user.device_token;
            await _userManager.UpdateAsync(existingUser);

            if (existingUser!=null)
            {
                var jwtToken = GenerateJwtToken(existingUser);
                _logger.LogInformation("Logined");
                return new RegistrationResponseDTO()
                {
                    Result = true,
                    Token = jwtToken,
                    Id = existingUser.Id,
                    RolesName = rolename.ToList(),
                    UserName = existingUser.UserName
                };
            }
            else
            {
                string ErrorAuthentic = "Invalid authentication request";
                // We dont want to give to much information on why the request has failed for security reasons
                _logger.LogWarning(ErrorAuthentic);
                return new RegistrationResponseDTO()
                {
                    Result = false,
                    Errors = new List<string>(){
                           ErrorAuthentic
                        }
                };
            }

        }

        private async Task<IdentityResult> addAdmin()
        {
            ApplicationUser user;
            var existingUser = _unitOfWork.applicationUserManger.GetAllQurAsync().Where(r => r.PhoneNumber == "root" && r.isDeleted == false).FirstOrDefault();
            string AddEmail = "<EMAIL>";
            IdentityResult result = new IdentityResult();
            if (existingUser==null)
            {
                user = new ApplicationUser()
                {
                    Email = AddEmail,
                    UserName = AddEmail,
                    EmailConfirmed = true,
                    PhoneNumber = "root",
                    isDeleted = false,
                    Isapproved = true,
                    CreatedBy = "root",
                    Creation_Date = DateTime.Now,
                    LastEditeBy = "root",
                    LastEiteDate = DateTime.Now,
                    Birthdate = DateTime.Now,
                };
                result = await _userManager.CreateAsync(user, "123456789aA@");

            }
            var existingUser2 = _unitOfWork.applicationUserManger.GetAllQurAsync().Where(r => r.PhoneNumber == "123456789" && r.isDeleted == false).FirstOrDefault();
            if (existingUser2==null)
            {
                user = new ApplicationUser()
                {
                    Email = "<EMAIL>",
                    UserName = "<EMAIL>",
                    EmailConfirmed = true,
                    PhoneNumber = "123456789",
                    isDeleted = false,
                    Isapproved = true,
                    CreatedBy = "Sys",
                    Creation_Date = DateTime.Now,
                    LastEditeBy = "Sys",
                    LastEiteDate = DateTime.Now,
                    Birthdate = DateTime.Now,
                    UserType = (int)AllEnumes.UserType.MiniEmployee,

                };
                 result = await _userManager.CreateAsync(user, "123456789aA@");
            }
            //employee
           
            if (result.Succeeded)
            {
                _logger.LogInformation(AddEmail + " Is created ");
            }
            var resultAddAdminRole = await addAdminRole(AddEmail);
            return resultAddAdminRole;
        }
        private async Task<IdentityResult> addAdminRole(string AddEmail)
        {
            string RoleName;
            string AddRoleName = "SuperAdmin";
            ApplicationUser UserModel = new ApplicationUser();
            IEnumerable<IdentityRole> ListRole = new List<IdentityRole>();
            ListRole = await roleManager.Roles.ToListAsync();
            if (!(ListRole.Any(x => x.Name == AddRoleName)))
            {
                IdentityRole roles = new IdentityRole()
                {
                    Name = AddRoleName
                };
                var createRoleResult = await roleManager.CreateAsync(roles);
                RoleName = roles.Name;
                _logger.LogInformation(AddRoleName + " Is created ");
            }
            else
            {
                RoleName = ListRole.Where(x => x.Name == AddRoleName).FirstOrDefault().Name;
            }
            if (await _userManager.FindByEmailAsync(AddEmail) != null)
            {
                UserModel = await _userManager.FindByEmailAsync(AddEmail);
            }
            else
            {
                UserModel = await _userManager.FindByEmailAsync(AddEmail);
            }
            return await _userManager.AddToRoleAsync(UserModel, RoleName);
        }
        public async Task<List<ApplicationUser>> ListEmployeeUsers()
        {
            try
            {
                List<ApplicationUser> users = new List<ApplicationUser>();
                users = await _userManager.Users.Where(r=>r.isDeleted!= true&&r.UserType==1 && r.lastNameEn != null && r.FirstNameEn != null).ToListAsync();
                return users;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex.Message);
                throw;
            }
        }

        public async Task<ResultDTO<ApplicationUser>> CreateUser(UserDTO user)
        {
            string ErrorCreateUSer = "new Employee was not creaded";
            string PasswordComp = "123456aA@";
            var CreateCustomePass = user.Password + PasswordComp;

            var existingUser = _userManager.Users.Where(r => (r.UserName == user.UserName && r.isDeleted == false)).FirstOrDefault();
            if (existingUser != null||user.PhoneNumber=="123456789")
            {
                // We dont want to give to much information on why the request has failed for security reasons
                _logger.LogWarning("Username is  Exist");
              return   new ResultDTO<ApplicationUser>()
                {
                    Object = null,
                    Message = "Username is  Exist",
                    State = false
                };
            }
            try
            {
                ApplicationUser appUser = new ApplicationUser();
                appUser.UserName = user.UserName;
                appUser.Email = user.Email;
                appUser.EmailConfirmed = Convert.ToBoolean(user.EmailConfirmed);
                appUser.PhoneNumber = user.PhoneNumber;
                appUser.UserType = user.UserType;
                appUser.lastNameEn = user.LastName;
                appUser.lastNameAr = user.LastName;
                appUser.FirstNameEn = user.FirstName;
                appUser.FirstNameAr = user.FirstName;
                appUser.isDeleted= false;
                appUser.Image = _configuration["URL:Base"]+"api/Services/Display?name=user-Default-User.png&key=EmployeesImage";

                IdentityResult result = await _userManager.CreateAsync(appUser, CreateCustomePass);
                var Result = new ResultDTO<ApplicationUser>();
                string ErrorMessage = "new Employee was creaded";
                if (result.Succeeded)
                {
                    Result = new ResultDTO<ApplicationUser>()
                    {
                        Object = appUser,
                        Message = ErrorMessage,
                        State = true
                    };
                    _logger.LogInformation(ErrorMessage);

                }
                else
                {
                    foreach (IdentityError error in result.Errors)
                    {
                        var res = new Dictionary<string, dynamic>();
                        res.Add("AllEmployeesShifts", error);
                    }
                    Result = new ResultDTO<ApplicationUser>()
                    {
                        Object = appUser,
                        Message = ErrorCreateUSer,
                        State = false
                    };

                }
                _logger.LogError(result.Errors.ToList().ToString());
                return Result;

            }

            catch (Exception ex)
            {
                var Result = new ResultDTO<ApplicationUser>()
                {
                    Object = null,
                    Message = ErrorCreateUSer,
                    State = false
                };
                _logger.LogError(ex.Message);
                return Result;
            }
        }

        public async Task<ResultDTO<UserDTO>> Details(string id)
        {
            var Result = new ResultDTO<UserDTO>();
            try
            {
                ApplicationUser user = await _userManager.FindByIdAsync(id);

                if (user != null)
                {
                    UserDTO Model = new UserDTO()
                    {
                        Id = user.Id,
                        UserName = user.Email,
                        Email = user.Email,
                        FirstName = user.FirstNameEn,
                        LastName = user.lastNameEn,
                        PhoneNumber = user.PhoneNumber,
                        EmailConfirmed = Convert.ToString(user.EmailConfirmed),
                        UserType = user.UserType
                    };
                    string Message = "Get Details of an Employee";
                    _logger.LogInformation(Message + id);
                    Result = new ResultDTO<UserDTO>()
                    {
                        Message = Message + id,
                        State = true,
                        Object = Model
                    };

                    return Result;
                }
                else
                {
                    string ErrorMessage = "Model is not valid";
                    _logger.LogInformation(ErrorMessage);
                    Result = new ResultDTO<UserDTO>()
                    {
                        Message = ErrorMessage + id,
                        State = false,
                        Object = null
                    };
                    return Result;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex.Message);
                Result = new ResultDTO<UserDTO>()
                {
                    Message = ex.Message,
                    State = false,
                    Object = null
                };
                return Result;
            }
        }

        public async Task<ResultDTO<Dictionary<string, dynamic>>> Update(EditUserDTO model)
        {
            ApplicationUser user = await _userManager.FindByIdAsync(model.Id);
            ApplicationUser mobieExist = _unitOfWork.applicationUserManger.GetAllQurAsync().Where(res => res.PhoneNumber == model.PhoneNumber && res.isDeleted == false).FirstOrDefault();
            var res = new Dictionary<string, dynamic>();
            var Result = new ResultDTO<Dictionary<string, dynamic>>();
            var existingFingerPrint = _unitOfWork.applicationUserManger.GetAllQurAsync().Where(r => r.fingerPrintIdAndroid == model.fingerPrintIdAndroid && r.isDeleted == false).FirstOrDefault();
            model.fingerPrintIdAndroid = model.fingerPrintIdAndroid == "" ? null : model.fingerPrintIdAndroid;
            if (mobieExist != null && user.PhoneNumber != model.PhoneNumber)
            {
                string ErrorMessage = "This mobile phone cannot be updated because it exists";
                res.Add("Errors", ErrorMessage);
                _logger.LogError(ErrorMessage);
                Result = new ResultDTO<Dictionary<string, dynamic>>()
                {
                    Message = ErrorMessage,
                    State = false,
                    Object = res
                };
                return Result;
            }
            else if (existingFingerPrint != null && user.fingerPrintIdAndroid != model.fingerPrintIdAndroid)
            {
                string ErrorMessage = "This finger Print cannot be updated because it exists !";
                res.Add("Errors", ErrorMessage);
                _logger.LogError(ErrorMessage);
                Result = new ResultDTO<Dictionary<string, dynamic>>()
                {
                    Message = ErrorMessage,
                    State = false,
                    Object = res
                };
                return Result;
            }
            else
            {
                try
                {
                    if (user != null)
                    {
                        user.Email = model.Email == "" ? user.Email : model.Email;
                        user.lastNameEn = model.LastName == "" ? user.lastNameEn : model.LastName;
                        user.FirstNameEn = model.FirstName == "" ? user.FirstNameEn : model.FirstName;
                        user.PhoneNumber = model.PhoneNumber == "" ? user.PhoneNumber : model.PhoneNumber;
                        user.EmailConfirmed = Convert.ToBoolean(user.EmailConfirmed);
                        user.fingerPrintIdAndroid = model.fingerPrintIdAndroid==""? user.fingerPrintIdAndroid: model.fingerPrintIdAndroid;
                        if (!string.IsNullOrEmpty(user.Email))
                        {
                            user.Email = model.Email;
                        }

                        else
                        {
                            string ErrorMessage = "Email cannot be empty";
                            _logger.LogError(ErrorMessage);
                            res.Add("Errors", ErrorMessage);
                            Result = new ResultDTO<Dictionary<string, dynamic>>()
                            {
                                Message = ErrorMessage,
                                State = false,
                                Object = res
                            };
                            return Result;
                        }

                        if (!string.IsNullOrEmpty(model.Email))
                        {
                            IdentityResult result = await _userManager.UpdateAsync(user);
                            string Message = "Succefuly Updated";
                            if (result.Succeeded)
                            {
                                res.Add("Message", Message);
                                _logger.LogInformation(Message);
                                Result = new ResultDTO<Dictionary<string, dynamic>>
                                {
                                    Message = Message,
                                    State = true,
                                    Object = res
                                };
                                return Result;
                            }
                            else
                            {
                                res.Add("Error", Message);
                                var arrErrList = result.Errors.ToArray();
                                _logger.LogError(arrErrList.ToString());
                                Result = new ResultDTO<Dictionary<string, dynamic>>
                                {
                                    Message = arrErrList.ToString(),
                                    State = false,
                                    Object = res
                                };
                                return Result;
                            }
                        }
                        else
                        {
                            string ErrorMessage = "Error in Updating";
                            res.Add("Error", ErrorMessage);
                            _logger.LogError(ErrorMessage);
                            Result = new ResultDTO<Dictionary<string, dynamic>>
                            {
                                Message = ErrorMessage,
                                State = false,
                                Object = res
                            };
                            return Result;
                        }
                    }
                    else
                    {
                        string ErrorMessage = "User Not Found";
                        _logger.LogError(ErrorMessage);
                        Result = new ResultDTO<Dictionary<string, dynamic>>()
                        {
                            Message = ErrorMessage,
                            State = false,
                            Object = null
                        };
                        return Result;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex.Message);
                    Result = new ResultDTO<Dictionary<string, dynamic>>()
                    {
                        Message = ex.Message,
                        State = false,
                        Object = null
                    };
                    return Result;
                }
            }
        }

        public async Task<ResultDTO<Dictionary<string, dynamic>>> SignEmployeeToServices(SignEmployeeToServicesDTO Model)
        {
            try
            {
                var result = _unitOfWork.applicationUserManger.GetAllQurAsync().Where(x => x.Id == Model.EemployeeId && x.isDeleted == false).FirstOrDefault();
                EmpServ Mdeolll;
                var Result = new ResultDTO<Dictionary<string, dynamic>>();
                for (int i = 0; i < Model.ServicesListId.Count; i++)
                {
                    Mdeolll = new EmpServ()
                    {
                        EmployeeId = Model.EemployeeId,
                        ServiceId = Model.ServicesListId[i],
                        CreatedBy = Model.CreatedBy,
                        Creation_Date = DateTime.Now,
                        isDeleted = false
                    };
                    result.EmpServ.Add(Mdeolll);
                }
                var resultUpdate = await _unitOfWork.applicationUserManger.UpdateAsync(result);
                if (resultUpdate)
                {
                    _logger.LogInformation("Updated");
                    Result = new ResultDTO<Dictionary<string, dynamic>>()
                    {
                        Message = "Updated",
                        State = true,
                        Object = null
                    };
                    return Result;
                }
                else
                {
                    string Error = "an error occurred";
                    Result = new ResultDTO<Dictionary<string, dynamic>>()
                    {
                        Message = Error,
                        State = false,
                        Object = null
                    };
                    _logger.LogError(Error);
                    return Result;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex.Message);
                var Result = new ResultDTO<Dictionary<string, dynamic>>()
                {
                    Message = ex.Message,
                    State = false,
                    Object = null
                };
                return Result;
            }
        }

        public async Task<ResultDTO<Dictionary<string, dynamic>>> DeleteUser(string id)
        {
            ApplicationUser user = await _userManager.FindByIdAsync(id);
            var Result = new ResultDTO<Dictionary<string, dynamic>>();
            var res = new Dictionary<string, dynamic>();
            if (user != null)
            {
                if (user.isDeleted == true)
                {
                    _logger.LogInformation("Deleted");
                    res.Add("Message", "User has already been deleted");
                    Result = new ResultDTO<Dictionary<string, dynamic>>()
                    {
                        Message = "User has already been deleted",
                        State = false,
                        Object = res
                    };
                    return Result;
                }

                user.isDeleted = true;
                IdentityResult result = await _userManager.UpdateAsync(user);
                if (result.Succeeded)
                {
                    _logger.LogInformation("Deleted");
                    res.Add("Message", "Deleted");
                    Result = new ResultDTO<Dictionary<string, dynamic>>()
                    {
                        Message = "Deleted",
                        State = true,
                        Object = res
                    };
                    return Result;
                }
                else
                {
                    string Error = "an error occurred";
                    _logger.LogError(Error);
                    res.Add("Error", "Deleted");
                    Result = new ResultDTO<Dictionary<string, dynamic>>()
                    {
                        Message = "an error occurred",
                        State = false,
                        Object = res
                    };
                    return Result;
                }
            }
            else
            {
                string Error = "User Not Found";
                _logger.LogError(Error);
                res.Add("Error", Error);
                Result = new ResultDTO<Dictionary<string, dynamic>>()
                {
                    Message = "an error occurred",
                    State = false,
                    Object = res
                };
                return Result;
            }
        }

        public async Task<List<ApplicationUser>> GetCustomer()
        {

            try
            {
                List<ApplicationUser> customers = new List<ApplicationUser>();
                customers = await _userManager.Users.Where(r => r.isDeleted == false && r.UserType== (int)AllEnumes.UserType.Customer).ToListAsync();
                return customers;
            }
            catch (Exception ex)
            {

                _logger.LogError(ex.Message);
                throw;
            }
        }

        public async Task<ResultDTO<Dictionary<string, dynamic>>> GetUsersByName(string name)
        {
            var res = new Dictionary<string, dynamic>();
            var list = await _userManager.Users.Where(r => r.isDeleted != true).ToListAsync();
            var userslist = new List<ApplicationUser>();
            foreach (var item in list)
            {
                var UserName = item.UserName.ToLower().Contains(name.ToLower());
                if (UserName == true )
                {
                    userslist.Add(item);
                }
            }
            res.Add("usersData", userslist);
            var Obj = new ResultDTO<Dictionary<string, dynamic>>()
            {
                Object = res,
                Message = "Done",
                State = true
            };
            return Obj;
        }

    }
}
