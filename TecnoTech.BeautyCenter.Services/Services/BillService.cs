using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TecnoTech.BeautyCenter.DAL.SqlServer.EnumsModel;
using TecnoTech.BeautyCenter.DAL.SqlServer.Models;
using TecnoTech.BeautyCenter.DTOs.CoreDTO;
using TecnoTech.BeautyCenter.DTOs.DTOModel;
using TecnoTech.BeautyCenter.UnitOfWork.unitOfWork;

namespace TecnoTech.BeautyCenter.Services.Services
{
    public class BillService : IBillService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<BillService> _logger;
        private readonly IConfiguration _Configure;
        private readonly IServicesService _ServicesService;

        public BillService(IUnitOfWork unitOfWork, ILogger<BillService> logger, IServicesService ServicesService, IConfiguration configure)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
            _Configure = configure;
            _ServicesService = ServicesService;

        }

        public async Task<ResultDTO<BillDTO>> PayBill(PayBillDTO billDTO)
        {
            ApplicationUser Customer = await _unitOfWork.applicationUserManger.GetAllQurAsync().Where(w => w.PhoneNumber == billDTO.PhoneNumber && w.isDeleted == false).FirstOrDefaultAsync();
            if (Customer == null)
            {
                Customer = await _unitOfWork.applicationUserManger.GetAllQurAsync().Where(w => w.Id == billDTO.CustomerId && w.isDeleted == false).FirstOrDefaultAsync();
            }
            ResultDTO<BillDTO> BillDTO;
            if (Customer == null)
            {
              return  BillDTO = new ResultDTO<BillDTO>()
                {
                    Message ="this customer Dos not  Here",
                    Object = null,
                    State = false
                };                
            }
            Bill bill = new Bill()
            {
                CustomerId = Customer.Id,
                CreatedBy = billDTO.CreateBy,
                Isapproved = true,
                isDeleted = false
            };
            var BillModel = await _unitOfWork.BillManger.AddAsync(bill);
            bool Conde = BillModel != null;
            BillItems billItems;
            BillDTO dTO = new BillDTO();
            if (Conde)
            {
                List<BillItems> BillItemsList = new List<BillItems>();
                var AppointmentModel = _unitOfWork.AppointmentManger.GetAllQurAsync().Where(r => r.isDeleted == false);
                Appointment appointment;
                for (int i = 0; i < billDTO.AppointmentDTO.Count; i++)
                {
                    appointment = await AppointmentModel
                        .Where(s => s.Id == billDTO.AppointmentDTO[i].AppointmentId && s.State == (int)AllEnumes.StateAppointments.Confirmed && s.isDeleted == false)
                        .FirstOrDefaultAsync();
                    if (appointment != null)
                    {
                        billItems = new BillItems()
                        {
                            BillId = BillModel.Id,
                            appointmentId = billDTO.AppointmentDTO[i].AppointmentId,
                            CreatedBy = billDTO.CreateBy,
                            ItemPrice = billDTO.AppointmentDTO[i].AppointmentPrice,
                            Isapproved = true,
                            isDeleted = false,
                            LastEditeBy = billDTO.CreateBy,
                            LastEiteDate = DateTime.Now
                        };
                        BillModel.Amount += billItems.ItemPrice;
                        BillItemsList.Add(billItems);

                        //add Completed Appointment after paybill
                        var data = _unitOfWork.AppointmentManger.GetAllQurAsync().Where(re => re.Id == appointment.Id && re.isDeleted == false).Include(w => w.Service).Include(e => e.Customer).FirstOrDefault();
                        data.State = (int)AllEnumes.StateAppointments.Completed;
                        var ResultUpdated = await _unitOfWork.AppointmentManger.UpdateAsync(data);
                    }
                    else
                    {
                        BillDTO = new ResultDTO<BillDTO>()
                        {
                            Message = "Somthing Happen in you submition my be this job paid before please cheak your Selection and try agin",
                            Object = null,
                            State = false
                        };
                        return BillDTO;
                    }                   
                }

                var UpdatedBill = await _unitOfWork.BillManger.GetByAsync(BillModel.Id);
                UpdatedBill.Amount = BillModel.Amount;
                int NewPoints =(int) dTO.Amount / 5;
                var NewTotal = NewPoints + Customer.Points;
                BillModel.Customer.Points = NewTotal;
                var UpdatedBillModel = await _unitOfWork.BillManger.UpdateAsync(UpdatedBill);
                var billItemsModel = await _unitOfWork.BillItemsManger.AddRengAsync(BillItemsList);
                dTO = new BillDTO()
                {
                    Amount = BillModel.Amount,
                };
                Conde = UpdatedBill != null && UpdatedBillModel;               
            }
            List<string> FBT = new List<string>();
            FBT.Add(Customer.device_token);
            int Points = (int)dTO.Amount / 5;
            var Total = Points + Customer.Points;
            var notification = new SendNotification()
            {
                 FirebaseToken = FBT,
               // FirebaseToken = Customer.device_token,
                body = "تم دفع مبلغ ("+ dTO.Amount+ ")ج وتم إضافه ("+ Points + ") نقطه على رصيدك الاساسى ("+ Customer.Points + ") ليصبح رصيدك ("+Total+") المتاح",
                image = "",
                ServerKey = _Configure["firebase:ServerKey"],
                SenderId = _Configure["firebase:SenderId"]
            };
            var notifecation = _ServicesService.SendNotification(notification);
            BillDTO = new ResultDTO<BillDTO>()
            {
                Message = "Done",
                Object = dTO ,
                State = Conde
            };
            return BillDTO;
        }
    }
   
}
