using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TecnoTech.BeautyCenter.DAL.SqlServer.Models;
using TecnoTech.BeautyCenter.DTOs.CoreDTO;
using TecnoTech.BeautyCenter.DTOs.DTOModel;

namespace TecnoTech.BeautyCenter.Services.Services
{
    public interface IEmployeesService
    {
        Task<ResultDTO<Dictionary<string, dynamic>>> GetEmployeeByServiceId(int requestId);
        Task<ResultDTO<Dictionary<string, dynamic>>> GetEmployeeShift(string requestId);
        Task<ResultDTO<Dictionary<string, dynamic>>> GetSlotsEmployeeShift(string requestId);

        Task<ResultDTO<Dictionary<string, dynamic>>> GetAllEmployeesShifts();
        Task<ResultDTO<Dictionary<string, dynamic>>> UpdateEmployeeShift(ShiftDTO entity);
        Task<ResultDTO<Dictionary<string, dynamic>>> CheckSwapEmployeeShift(ShiftEmpDTO entity);
        Task<ResultDTO<Dictionary<string, dynamic>>> CheckChangeShift(ShiftEmpDTO entity);


    }
}
