using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TecnoTech.BeautyCenter.DAL.SqlServer.Models;
using TecnoTech.BeautyCenter.DTOs.CoreDTO;
using TecnoTech.BeautyCenter.DTOs.DTOModel;
using TecnoTech.BeautyCenter.UnitOfWork.unitOfWork;

namespace TecnoTech.BeautyCenter.Services.Services
{
    public class NotificationService : INotificationService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<NotificationService> _logger;
        public NotificationService(IUnitOfWork unitOfWork, ILogger<NotificationService> logger)
        {
            _logger = logger;
            _unitOfWork = unitOfWork;
        }
        public async Task<ResultDTO<Dictionary<string, dynamic>>> GetAll()
        {
            var res = new Dictionary<string, dynamic>();

            var list = await _unitOfWork.NotificationManger.GetAllQurAsync().Where(r => r.isDeleted == false).ToListAsync();
            res.Add("NotificationsData", list);
            var Obj = new ResultDTO<Dictionary<string, dynamic>>()
            {
                Object = res,
                Message = "Done",
                State = true
            };
            return Obj;
        }

        public Task<ResultDTO<Dictionary<string, dynamic>>> GetNotificationById(int userId)
        {
            throw new NotImplementedException();
        }

        public async Task<ResultDTO<Dictionary<string, dynamic>>> Insert(NotificationsDTO notification)
        {
            var res = new Dictionary<string, dynamic>();
            Notification notif = new Notification()
            {
                ServiceName = notification.ServiceName,
                AppointmentDate = notification.AppointmentDate,
                AppointmentId = notification.AppointmentId,
                CustomerId = notification.CustomerId,
                CustomerName = notification.CustomerName,
                EmployeeId = notification.EmployeeId,
                EmployeeName = notification.EmployeeName,
                From = notification.From,
                To = notification.To,
                Creation_Date = DateTime.Now,
                LastEiteDate =notification.LastEiteDate,
                isDeleted=notification.IsDeleted,
                ServiceId=notification.ServiceId,   
                CustomerPhone=notification.CustomerPhone,
            };

            var result =  _unitOfWork.NotificationManger.Add(notif);
            if (result != null)
            {
                res.Add("NotificationData", result);
                var Obj = new ResultDTO<Dictionary<string, dynamic>>()
                {
                    Object = res,
                    Message = "Done",
                    State = true
                };
                return Obj;
            }
            else
            {
                res.Add("NotificationData", null);
                var Obj = new ResultDTO<Dictionary<string, dynamic>>()
                {
                    Object = res,
                    Message = "Not Done",
                    State = false
                };
                return Obj;
            }
        }


        public async Task<ResultDTO<Dictionary<string, dynamic>>> Update(NotificationsEditDTO notifications)
        {
            var res = new Dictionary<string, dynamic>();
            var notification = _unitOfWork.NotificationManger.GetBy(notifications.Id);
            notification.isDeleted = notifications.IsDeleted;
            notification.LastEiteDate = DateTime.Now;
            var result = await _unitOfWork.NotificationManger.UpdateAsync(notification);
            if (result != null)
            {
                res.Add("NotificationData", result);
                var Obj = new ResultDTO<Dictionary<string, dynamic>>()
                {
                    Object = res,
                    Message = "Done",
                    State = true
                };
                return Obj;
            }
            else
            {
                res.Add("NotificationData", null);
                var Obj = new ResultDTO<Dictionary<string, dynamic>>()
                {
                    Object = res,
                    Message = "Not Done",
                    State = false
                };
                return Obj;
            }
        }
    }
}
