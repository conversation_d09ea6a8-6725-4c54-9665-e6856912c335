using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TecnoTech.BeautyCenter.Services.Services
{
    public interface IGeneralService
    {
        void ResizeAndSaveImage(Stream stream, string filename);
       string getPath(string ReferencePath);
        Task<dynamic> saveImageData(string ReferenceId, string ReferencePath, string ReferenceKey, string FileName, string ContentType, string CreatedBy);

    }
}
