using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TecnoTech.BeautyCenter.DAL.SqlServer.Models;
using TecnoTech.BeautyCenter.DTOs.Administration;
using TecnoTech.BeautyCenter.DTOs.CoreDTO;
using TecnoTech.BeautyCenter.DTOs.DTOModel;
using TecnoTech.BeautyCenter.DTOs.DTOModel.Requests.FromMobile;
using TecnoTech.BeautyCenter.DTOs.DTOModel.Response;

namespace TecnoTech.BeautyCenter.Services.Services
{
    public interface IAccountentService
    {
        public Task<RegistrationResponseDTO> Register(RegistrationRequestDTO user);
        public Task<RegistrationResponseDTO> ResetPassword(ResetPassword user);
        public Task Logout();
        public Task<RegistrationResponseDTO> Login(LoginRequestDTO user);
        public Task<RegistrationResponseDTO> LoginFingerPrint(LoginFingerPrint user);

        public Task<List<ApplicationUser>> ListEmployeeUsers();
        public Task<List<ApplicationUser>> GetCustomer();

        public Task<ResultDTO<ApplicationUser>> CreateUser(UserDTO user);
        public Task<ResultDTO<UserDTO>> Details(string id);
        public Task<ResultDTO<Dictionary<string, dynamic>>> Update(EditUserDTO model);
        public Task<ResultDTO<Dictionary<string, dynamic>>> SignEmployeeToServices(SignEmployeeToServicesDTO Model);
        public Task<ResultDTO<Dictionary<string, dynamic>>> DeleteUser(string id);
        public Task<ResultDTO<Dictionary<string, dynamic>>> GetUsersByName(string name);
    }
}
