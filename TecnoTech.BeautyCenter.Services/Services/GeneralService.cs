using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.PixelFormats;
using SixLabors.ImageSharp.Processing;
using TecnoTech.BeautyCenter.DAL.SqlServer.Models;
using TecnoTech.BeautyCenter.UnitOfWork.unitOfWork;

namespace TecnoTech.BeautyCenter.Services.Services
{
    public class GeneralService : IGeneralService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IConfiguration _Configure;

        public GeneralService(IUnitOfWork unitOfWork, IConfiguration configure)
        {
            _unitOfWork = unitOfWork;
            _Configure = configure;
        }
        public void ResizeAndSaveImage(Stream stream, string filename)
        {
            using (Image<Rgba32> image = Image.Load<Rgba32>(filename))
            {
                image.Mutate(x => x
                     .Resize(image.Width / 4, image.Height / 4)
                 );
                image.Save(filename); // Automatic encoder selected based on extension.
            }
        }
        public  string getPath(string ReferencePath)
        {
            dynamic uploadDirecotroy = "";
            if (ReferencePath == "BGPhoto")
            {
                uploadDirecotroy = _Configure["URL:BGPhoto"];
            }
            else if (ReferencePath == "IconService")
            {
                uploadDirecotroy = _Configure["URL:IconService"];

            }
            else if (ReferencePath == "ImageEmp")
            {
                uploadDirecotroy = _Configure["URL:ImageEmp"];
            }
            else if (ReferencePath == "ImageNotification")
            {
                uploadDirecotroy = _Configure["URL:ImageNotification"];
            }
            return uploadDirecotroy;
        }
        public async Task< dynamic > saveImageData(string ReferenceId, string ReferencePath, string ReferenceKey, string FileName, string ContentType, string CreatedBy)
        {
            if (ReferencePath == "BGPhoto")
            {
                var ExistPhoto = _unitOfWork.GeneralPhotosManger.GetAllQurAsync().Where(r => r.ReferenceKey == ReferenceKey && r.isDeleted == false).FirstOrDefault();
                if (ExistPhoto != null)
                {
                    ExistPhoto.UrlImage = _Configure["URL:DisplayImage"] + FileName + "&key=BackgroundPhoto";
                    var res =await _unitOfWork.GeneralPhotosManger.UpdateAsync(ExistPhoto);
                    return res;
                }
                else
                {
                    GeneralPhoto generalPhoto = new GeneralPhoto()
                    {
                        ReferenceKey = ReferenceKey,
                        UrlImage = _Configure["URL:DisplayImage"] + FileName + "&key=BackgroundPhoto",
                        Type = ContentType,
                        CreatedBy = CreatedBy,
                        Creation_Date = DateTime.Now,
                    };
                    var res = _unitOfWork.GeneralPhotosManger.Add(generalPhoto);
                    return res;

                }
            }
            else if (ReferencePath == "IconService")
            {
                var service = _unitOfWork.serviceManger.GetAllQurAsync().Where(e => e.Id == int.Parse(ReferenceId) && e.isDeleted == false).FirstOrDefault();
                service.Icon = _Configure["URL:DisplayImage"] + FileName + "&key=ServicesIcons";
                var Results = _unitOfWork.serviceManger.Update(service);
                return Results;

            }
            else if (ReferencePath == "ImageEmp")
            {
                var emp = _unitOfWork.applicationUserManger.GetAllQurAsync().Where(e => e.Id == ReferenceId && e.isDeleted == false).FirstOrDefault();
                emp.Image = _Configure["URL:DisplayImage"] + FileName + "&key=EmployeesImage";
                var Results = _unitOfWork.applicationUserManger.Update(emp);
                return Results;

            }
            else if (ReferencePath == "ImageNotification")
            {
                GeneralPhoto generalPhoto = new GeneralPhoto()
                {
                    ReferenceKey = ReferenceKey,
                    UrlImage = _Configure["URL:DisplayImage"] + FileName + "&key=ImageNotification",
                    Type = ContentType,
                    CreatedBy = CreatedBy,
                    Creation_Date = DateTime.Now,
                };
                var res = _unitOfWork.GeneralPhotosManger.Add(generalPhoto);
                var resD = new Dictionary<string, dynamic>()
                {
                    {"UrlImage" ,res.UrlImage},
                    {"Name" ,FileName},

                };
                return resD;
            }
            else
            {
                return null;
            }
        }
    }

    public class SaveImageDTO
    {
        public string ReferenceId { get; set; }
        public string ReferencePath { get; set; }
        public string ReferenceKey { get; set; }
        public string FileName { get; set; }
        public string ContentType { get; set; }
        public string CreatedBy { get; set; }
    }
}
