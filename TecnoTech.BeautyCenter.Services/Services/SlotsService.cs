using Microsoft.AspNetCore.Identity;
using Microsoft.CodeAnalysis.Differencing;
using Microsoft.Data.SqlClient.Server;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.VisualBasic;
using Microsoft.VisualStudio.Web.CodeGeneration.Contracts.Messaging;
using Nancy.Diagnostics;
using Nancy.Extensions;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Linq;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;
using TecnoTech.BeautyCenter.DAL.SqlServer.EnumsModel;
using TecnoTech.BeautyCenter.DAL.SqlServer.Models;
using TecnoTech.BeautyCenter.DTOs.CoreDTO;
using TecnoTech.BeautyCenter.DTOs.DTOModel;
using TecnoTech.BeautyCenter.UnitOfWork.unitOfWork;



namespace TecnoTech.BeautyCenter.Services.Services
{
    public class SlotsService : ISlotsService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<SlotsService> _logger;
        private readonly ITimelineService timelineService;
        private readonly ISettingsService SettingsService;
        private readonly IConfiguration _Configure;
        List<SlotsDTO> listslots = new List<SlotsDTO>();

        public SlotsService(IUnitOfWork unitOfWork,
            ILogger<SlotsService> logger,
            ITimelineService timelineService,
            IConfiguration configure,
            ISettingsService settingsService)
        {
            _logger = logger;
            _unitOfWork = unitOfWork;
            this.timelineService = timelineService;
            _Configure = configure;
            SettingsService = settingsService;
        }
        public async Task<ResultDTO<Dictionary<string, dynamic>>> GetAllAvailableSlots(AppointmentsRequestDTO requestDTO)
        {
            var resErrors = new Dictionary<string, dynamic>();
            if (requestDTO.ServiceId != 0)
            {
                if (requestDTO.DemandDate.Date < DateTime.Now.Date)
                {
                    string errorMessage = "Please chose date of your demand and  plase  the date  must be  in the  future";

                    resErrors.Add("Errors", errorMessage);
                    //return BadRequest("Please chose date of your demand and  plase  the date  must be  in the  future");
                    _logger.LogError(errorMessage);

                    return ResultDTODictionary(resErrors, "Not Done", false);
                }
                var Services = await _unitOfWork.serviceManger.GetByAsync(requestDTO.ServiceId);
                if (Services == null)
                {
                    string errorMessage = "this services not found";
                    _logger.LogError(errorMessage);
                    resErrors.Add("Errors", errorMessage);
                    return ResultDTODictionary(resErrors, "Not Done", false);
                }
                IGrouping<int, SlotsDTO>[] array;
                var Slotss = new List<IGrouping<string, SlotsDTO>>();
                var List2 = new List<IGrouping<DateTime, SlotsDTO>>();
                array = SlotMangerGetAll(requestDTO);

                AvailableAppointMent availableAppointments = new AvailableAppointMent();
                List<AvailableAppointMent> availableAppointmentss = new List<AvailableAppointMent>();
                List<SlotsDTO> listslots = new List<SlotsDTO>();

               var availableSlots = CreateAvailableAppointment(array, Services.SlotsNumber);
                //availableAppointmentss = availableSlots;
                var time = DateTime.Now.TimeOfDay;
               
                    if (requestDTO.DemandDate == DateTime.Now.Date)
                    {
                        availableAppointmentss = availableSlots.Where(r => r.from >= TimeSpan.FromTicks(time.Ticks)).ToList();

                    }
                    else
                    {
                        availableAppointmentss = availableSlots;
                    }
                

                var Modelslot = _unitOfWork.GeneralPhotosManger.GetAllQurAsync().Where(res => res.ReferenceKey == "BgSlots" && res.isDeleted == false).ToList();
                var Result = new Dictionary<string, dynamic>()
                    {
                        {"backgroundphoto", Modelslot},
                        {"availableSlots", availableAppointmentss.OrderBy(r=>r.from)}
                    };

                return ResultDTODictionary(Result, "Done", true);
            }
            else
            {
                return ResultDTODictionary(null, "Not Done", false);
            }
        }
        private List<AvailableAppointMent> CreateAvailableAppointment(IGrouping<int, SlotsDTO>[] array, int servicesSlotsNumber)
        {
            var List2 = new List<IGrouping<DateTime, SlotsDTO>>();
            //List<SlotsDTO> listslots = new List<SlotsDTO>();
            AvailableAppointMent availableAppointments = new AvailableAppointMent();
            List<AvailableAppointMent> availableAppointmentss = new List<AvailableAppointMent>();
            for (int i = 0; i < array.Count(); i++)
            {
                var list = array[i].ToList();
                List2 = array[i].GroupBy(x => x.slotsDate).ToList();
                for (int q = 0; q < List2.Count; q++)
                {
                    for (int w = 0; w < List2[q].Count(); w++)
                    {
                        var ShiftSlots = List2[q].OrderBy(x => x.start).ToArray();
                        if (servicesSlotsNumber == 0)
                        {
                            break;
                        }
                        listslots = GenrateSlots(ShiftSlots[w]);
                        //var xxx=_unitOfWork.ShiftManger
                        //        .GetAllQurAsync().Where(r=>r.isDeleted==false && r.Id == listslots[0].ShiftId).ToListAsync();
                            if (listslots.Count == servicesSlotsNumber)
                            {
                        var Shift = _unitOfWork.ShiftManger.GetAllQurAsync().Where(r => r.Id == listslots[0].ShiftId && r.isDeleted == false).FirstOrDefaultAsync();
                        if (Shift.Result !=null)
                        {
                                availableAppointments = new AvailableAppointMent();
                                var sortedList = listslots.OrderBy(x => x.start);
                                availableAppointments.from = sortedList.FirstOrDefault().start;
                                availableAppointments.to = sortedList.LastOrDefault().end;
                                availableAppointments.AppointmentDate = listslots[0].slotsDate.ToString("dd/MM/yyyy");
                                availableAppointments.EmployeeId = _unitOfWork.ShiftManger
                                    .GetAllQurAsync()
                                    .Where(r => r.isDeleted == false)
                                    .Include(x => x.Employee)
                                    .Where(x => x.Id == listslots[0].ShiftId)
                                    .FirstOrDefaultAsync().Result.EmployeeId;

                                listslots.ForEach(x => availableAppointments.AppointmentslotsList.Add(x));
                                var IsExist = availableAppointmentss.Where(r => r.from == availableAppointments.from && r.to == availableAppointments.to && r.AppointmentDate == availableAppointments.AppointmentDate).ToList();
                                if (IsExist.Count == 0)
                                {
                                    availableAppointmentss.Add(availableAppointments);

                                }
                                listslots = new List<SlotsDTO>();
                            }

                        }
                    }
                }
            }
            return availableAppointmentss;
        }
        private List<SlotsDTO> GenrateSlots(SlotsDTO ShiftSlots)
        {
            //List<SlotsDTO> listslots = new List<SlotsDTO>();
            if (listslots.Count != 0)
            {
                if (listslots.LastOrDefault().end == ShiftSlots.start &&
                    listslots.LastOrDefault().slotsDate == ShiftSlots.slotsDate)
                {
                    if (ShiftSlots.slotsDate.Date == DateTime.Now.Date)
                    {
                        if (ShiftSlots.start >= DateTime.Now.TimeOfDay)
                        {
                            listslots.Add(ShiftSlots);

                        }
                    }
                    else
                    {
                        listslots.Add(ShiftSlots);
                    }
                }
                else
                {
                    listslots = new List<SlotsDTO>();
                    listslots.Add(ShiftSlots);
                }
            }
            if (listslots.Count == 0)
            {
                if (ShiftSlots.slotsDate.Date == DateTime.Now.Date)
                {                   
                    if (ShiftSlots.start >= DateTime.Now.TimeOfDay)
                    {
                        listslots.Add(ShiftSlots);
                    }
                }
                else
                {
                    listslots.Add(ShiftSlots);
                }
            }
            return listslots;
        }
        //m.awad --edit Status Slots 21-09-2022 add (con.Status == (int)AllEnumes.State.Pending || con.Status == (int)AllEnumes.State.reserved)
        private IGrouping<int, SlotsDTO>[] SlotMangerGetAll(AppointmentsRequestDTO requestDTO)
        {
            IGrouping<int, SlotsDTO>[] array;
            Expression<Func<SlotsDTO, bool>> Expreisson = con =>
                (con.Status == (int)AllEnumes.State.Available || con.Status == (int)AllEnumes.State.Pending || con.Status == (int)AllEnumes.State.reserved) &&
                con.slotsDate.Date >= DateTime.Now.Date &&
                con.slotsDate.Date == requestDTO.DemandDate;

            if (String.IsNullOrEmpty(requestDTO.EmployeeId) || String.IsNullOrWhiteSpace(requestDTO.EmployeeId))
            {
                array = _unitOfWork.SlotManger.GetAllQurAsync()
                    .Include(sh => sh.Shift).ThenInclude(emp => emp.Employee).ThenInclude(EmpServ => EmpServ.EmpServ)
                    .Where(s => s.Shift.Employee.EmpServ.Where(s => s.ServiceId == requestDTO.ServiceId).Any())
                    .Select(y => new SlotsDTO
                    {
                        Id = y.Id,
                        start = y.Start,
                        end = y.End,
                        ShiftId = y.ShiftId,
                        Status = y.Status,
                        slotsDate = y.SlotsDate
                    }).Where(Expreisson)
                    .OrderBy(x => x.start)
                    .AsEnumerable()
                    .GroupBy(s => s.ShiftId)
                    .ToArray();

            }
            else
            {
                array = _unitOfWork.SlotManger.GetAllQurAsync()
                    .Include(sh => sh.Shift).ThenInclude(emp => emp.Employee).ThenInclude(EmpServ => EmpServ.EmpServ)
                .Where(s => s.Shift.Employee.EmpServ.Where(s => s.ServiceId == requestDTO.ServiceId).Any() && s.Shift.EmployeeId == requestDTO.EmployeeId)
                .Select(y => new SlotsDTO
                {
                    Id = y.Id,
                    start = y.Start,
                    end = y.End,
                    ShiftId = y.ShiftId,
                    Status = y.Status,
                    slotsDate = y.SlotsDate
                }).Where(Expreisson)
                    .AsEnumerable()
                    .GroupBy(s => s.ShiftId)
                    .ToArray();


            }
            return array;

        }
        private ResultDTO<Dictionary<string, dynamic>> ResultDTODictionary(dynamic Object, string Message, bool state)
        {
            var Obj = new ResultDTO<Dictionary<string, dynamic>>()
            {
                Object = Object,
                Message = Message,
                State = state
            };
            return Obj;
        }
        private List<DateTime> GetDatesBetween(LongShiftDTO longShiftDTO)
        {
            var format = "dd/MM/yyyy";
            var formatInfo = new DateTimeFormatInfo()
            {
                ShortDatePattern = format
            };
            DateTime startDate =   Convert.ToDateTime(longShiftDTO.FromDate, formatInfo);
            DateTime endDate =  Convert.ToDateTime(longShiftDTO.ToDate, formatInfo);

            List<DateTime> allDates = new List<DateTime>();
            var re = Enum.GetName(typeof(DayOfWeek), 5);
            for (DateTime date = startDate; date <= endDate; date = date.AddDays(1))
                allDates.Add(date.Date);
            return allDates;
        }
        public async Task<ResultDTO<Dictionary<string, dynamic>>> CreateShift(List<RequestShiftDTO> requestShiftList)
        {

            bool SlotArray = false;
            Shift shift;

            var Result = new ResultDTO<Dictionary<string, dynamic>>();
            var res = new Dictionary<string, dynamic>();
            var format = "dd/MM/yyyy";
            var formatInfo = new DateTimeFormatInfo()
            {
                ShortDatePattern = format
            };
            for (int i = 0; i < requestShiftList.Count; i++)
            {
                shift = new Shift
                {
                    CreatedBy = requestShiftList[i].CreatedBy,
                    Creation_Date = DateTime.Now,
                    DayDate = Convert.ToDateTime(requestShiftList[i].dateTimeNow, formatInfo),
                    EmployeeId = requestShiftList[i].EmployeeId,
                    From = Convert.ToDateTime(requestShiftList[i].from).TimeOfDay,
                    Isapproved = false,
                    isDeleted = false,
                    LastEditeBy = requestShiftList[i].CreatedBy,
                    LastEiteDate = DateTime.Now,
                    State = (int)AllEnumes.State.Available
                };
                if (requestShiftList[i].ShiftType == null || requestShiftList[i].ShiftType == 0)
                {
                    shift.to = Convert.ToDateTime(requestShiftList[i].to).TimeOfDay;
                }
                else
                {
                    if (requestShiftList[i].ShiftType == (int)AllEnumes.ShiftTypes.ShiftA)
                    {
                        shift.to = Convert.ToDateTime(requestShiftList[i].from).AddHours((int)AllEnumes.ShiftTypeValue.ShiftA).TimeOfDay;
                    }
                    else if (requestShiftList[i].ShiftType == (int)AllEnumes.ShiftTypes.ShiftB)
                    {
                        shift.to = Convert.ToDateTime(requestShiftList[i].from).AddHours((int)AllEnumes.ShiftTypeValue.ShiftB).TimeOfDay;
                    }
                }


                var checkIfShiftIsExcist = _unitOfWork.ShiftManger.GetAllQurAsync().Where(x => x.EmployeeId == shift.EmployeeId && x.DayDate == shift.DayDate && x.isDeleted == false).ToArray();
                if (checkIfShiftIsExcist.Length == 0)
                {
                    var AddedShift = _unitOfWork.ShiftManger.Add(shift);
                    ShiftDTO shiftDTO = new ShiftDTO
                    {
                        dateTimeNow = DateTime.Now,
                        from = Convert.ToDateTime(requestShiftList[i].from),

                        Status = (int)AllEnumes.State.Available,
                        ShiftId = AddedShift.Id,
                        DayDate = shift.DayDate,
                        EmployeeId = AddedShift.EmployeeId
                    };
                    if (requestShiftList[i].ShiftType == null || requestShiftList[i].ShiftType == 0)
                    {
                        shiftDTO.to = Convert.ToDateTime(requestShiftList[i].to);

                    }
                    else
                    {
                        if (requestShiftList[i].ShiftType == (int)AllEnumes.ShiftTypes.ShiftA)
                        {
                            shiftDTO.to = Convert.ToDateTime(requestShiftList[i].from).AddHours((int)AllEnumes.ShiftTypeValue.ShiftA);
                        }
                        else if (requestShiftList[i].ShiftType == (int)AllEnumes.ShiftTypes.ShiftB)
                        {
                            shiftDTO.to = Convert.ToDateTime(requestShiftList[i].from).AddHours((int)AllEnumes.ShiftTypeValue.ShiftB);
                        }
                    }
                    var Slots = timelineService.GenerateSlots(shiftDTO);
                    SlotArray = _unitOfWork.SlotManger.AddReng(Slots);
                }
                else
                {
                    for (int l = 0; l < checkIfShiftIsExcist.Length; l++)
                    {
                        var Shift = await _unitOfWork.ShiftManger.GetAllQurAsync().Where(r => r.isDeleted == false).FirstOrDefaultAsync(j => j.Id == checkIfShiftIsExcist[l].Id);
                        var AppointmentSlot = await _unitOfWork.SlotManger.GetAllQurAsync()
                            .Where(p => (p.Status == (int)AllEnumes.State.reserved || p.Status == (int)AllEnumes.State.Done) && p.ShiftId == Shift.Id)
                            .ToArrayAsync();
                        if (AppointmentSlot.Length == 0)
                        {
                            Shift.DayDate = Convert.ToDateTime(requestShiftList[i].dateTimeNow, formatInfo);
                            Shift.From = Convert.ToDateTime(requestShiftList[i].from).TimeOfDay;
                            //Shift.to = Convert.ToDateTime(requestShiftList[i].to).TimeOfDay;
                            if (requestShiftList[i].ShiftType == null || requestShiftList[i].ShiftType == 0)
                            {
                                Shift.to = Convert.ToDateTime(requestShiftList[i].to).TimeOfDay;

                            }
                            else
                            {
                                if (requestShiftList[i].ShiftType == (int)AllEnumes.ShiftTypes.ShiftA)
                                {
                                    Shift.to = Convert.ToDateTime(requestShiftList[i].from).AddHours((int)AllEnumes.ShiftTypeValue.ShiftA).TimeOfDay;
                                }
                                else if (requestShiftList[i].ShiftType == (int)AllEnumes.ShiftTypes.ShiftB)
                                {
                                    Shift.to = Convert.ToDateTime(requestShiftList[i].from).AddHours((int)AllEnumes.ShiftTypeValue.ShiftB).TimeOfDay;
                                }
                            }

                            Shift.LastEditeBy = requestShiftList[i].CreatedBy;
                            Shift.LastEiteDate = DateTime.Now;
                            var UpdatedShift = await _unitOfWork.ShiftManger.UpdateAsync(Shift);
                            var RemoveSlots = await _unitOfWork.SlotManger.GetAllQurAsync().Where(d => d.ShiftId == Shift.Id).ToListAsync();
                            ShiftDTO shiftDTO = new ShiftDTO
                            {
                                dateTimeNow = DateTime.Now,
                                from = Convert.ToDateTime(requestShiftList[i].from),
                                //to = Convert.ToDateTime(requestShiftList[i].to),
                                Status = (int)AllEnumes.State.Available,
                                ShiftId = Shift.Id,
                                EmployeeId = Shift.EmployeeId,
                                DayDate = Shift.DayDate
                            };
                            if (requestShiftList[i].ShiftType == null || requestShiftList[i].ShiftType == 0)
                            {
                                shiftDTO.to = Convert.ToDateTime(requestShiftList[i].to);

                            }
                            else
                            {
                                if (requestShiftList[i].ShiftType == (int)AllEnumes.ShiftTypes.ShiftA)
                                {
                                    shiftDTO.to = Convert.ToDateTime(requestShiftList[i].from).AddHours((int)AllEnumes.ShiftTypeValue.ShiftA);
                                }
                                else if (requestShiftList[i].ShiftType == (int)AllEnumes.ShiftTypes.ShiftB)
                                {
                                    shiftDTO.to = Convert.ToDateTime(requestShiftList[i].from).AddHours((int)AllEnumes.ShiftTypeValue.ShiftB);
                                }
                            }
                            await _unitOfWork.SlotManger.RemoveRangeAsync(RemoveSlots);
                            var Slots = timelineService.GenerateSlots(shiftDTO);
                            SlotArray = _unitOfWork.SlotManger.AddReng(Slots);
                        }
                        else
                        {
                            _logger.LogWarning("this  shift can not be  editied  becouse  it in use");
                            res.Add("Error", "this  shift can not be  editied  becouse  it in use");
                            Result = new ResultDTO<Dictionary<string, dynamic>>()
                            {
                                Message = "this  shift can not be  editied  becouse  it in use",
                                Object = res,
                                State = false
                            };
                            return Result;
                        }
                    }
                }
            }
            res.Add("Message", "Slots Added");
            Result = new ResultDTO<Dictionary<string, dynamic>>()
            {
                Message = "Slots Added",
                Object = res,
                State = true
            };
            _logger.LogInformation("Slots Added");
            return Result;
        }
       
        public async Task<ResultDTO<Dictionary<string, dynamic>>> CreateShift(LongShiftDTO longShiftDTO)
        {

            bool SlotArray = false;
            Shift shift;
            
            var Result = new ResultDTO<Dictionary<string, dynamic>>();
            var res = new Dictionary<string, dynamic>();
            var format = "dd/MM/yyyy";
            var formatInfo = new DateTimeFormatInfo()
            {
                ShortDatePattern = format
            };
            List<DateTime> ShiftDate = new List<DateTime>();
            List<RequestShiftDTO> requestShiftList =await RequestShiftList(longShiftDTO);
                    
            for (int i = 0; i < requestShiftList.Count; i++)
            {
                var checkIfShiftIsExcist =await CheckIfShiftIsExcist(requestShiftList[i]);

                if (checkIfShiftIsExcist.Length == 0)
                {
                    shift = await FillShiftObj(requestShiftList[i], longShiftDTO.FromTime);
                    var AddedShift = _unitOfWork.ShiftManger.Add(shift);
                    ShiftDTO shiftDTO = await FillshiftdtoObj(AddedShift, requestShiftList[i], longShiftDTO);                      
                    var Slots = timelineService.GenerateSlots(shiftDTO);
                    SlotArray = _unitOfWork.SlotManger.AddReng(Slots);
                }
                else
                {
                    for (int l = 0; l < checkIfShiftIsExcist.Length; l++)
                    {
                        var Shift = await _unitOfWork.ShiftManger.GetAllQurAsync()
                            .Where(r => r.isDeleted == false)
                            .FirstOrDefaultAsync(j => j.Id == checkIfShiftIsExcist[l].Id);
                        var AppointmentSlot = await checkIfShiftInUse(Shift);
                        if (AppointmentSlot.Length == 0)
                        {
                            Shift.DayDate = Convert.ToDateTime(requestShiftList[i].dateTimeNow, formatInfo);
                            Shift.From = Convert.ToDateTime(requestShiftList[i].from).TimeOfDay;
                            if (requestShiftList[i].ShiftType == null || requestShiftList[i].ShiftType == 0)
                            {
                                Shift.to = Convert.ToDateTime(requestShiftList[i].to).TimeOfDay;
                            }
                            else
                            {
                                var ShiftType = await SettingsService.GetAllShiftTypeByIdNormal((int)(requestShiftList[i].ShiftType));
                                Shift.to = Convert.ToDateTime(longShiftDTO.FromTime).AddHours((int)ShiftType.Hours).TimeOfDay;
                            }
                            Shift.LastEditeBy = requestShiftList[i].CreatedBy;
                            Shift.LastEiteDate = DateTime.Now;
                            var UpdatedShift = await _unitOfWork.ShiftManger.UpdateAsync(Shift);
                            var RemoveSlots = await _unitOfWork.SlotManger.GetAllQurAsync().Where(d => d.ShiftId == Shift.Id).ToListAsync();
                            await _unitOfWork.SlotManger.RemoveRangeAsync(RemoveSlots);

                            ShiftDTO shiftDTO = await FillshiftdtoObj(Shift, requestShiftList[i], longShiftDTO);                               
                            var Slots = timelineService.GenerateSlots(shiftDTO);
                            SlotArray = _unitOfWork.SlotManger.AddReng(Slots);
                        }
                        else
                        {
                            _logger.LogWarning("this  shift can not be  editied  becouse  it in use");
                            res.Add("Error", "this  shift can not be  editied  becouse  it in use");
                            Result = new ResultDTO<Dictionary<string, dynamic>>()
                            {
                                Message = "this  shift can not be  editied  becouse  it in use",
                                Object = res,
                                State = false
                            };
                            return Result;
                        }
                    }
                }
            }
            res.Add("Message", "Slots Added");
            Result = new ResultDTO<Dictionary<string, dynamic>>()
            {
                Message = "Slots Added",
                Object = res,
                State = true
            };
            _logger.LogInformation("Slots Added");
            return Result;
        }

        private async Task<List<RequestShiftDTO>> RequestShiftList(LongShiftDTO longShiftDTO)
        {
            var format = "dd/MM/yyyy";
            var formatInfo = new DateTimeFormatInfo()
            {
                ShortDatePattern = format
            };
            List<DateTime> re = GetDatesBetween(longShiftDTO);
            List<RequestShiftDTO> requestShiftList = new List<RequestShiftDTO>();
            RequestShiftDTO requestShiftDTO;
            for (int i = 0; i < re.Count; i++)
            {
                int dayNum = (int)re[i].DayOfWeek;
                bool Daynum = longShiftDTO.Holydays.Contains(dayNum);
                if (!Daynum)
                {
                    requestShiftDTO = new RequestShiftDTO
                    {
                        dateTimeNow = re[i],
                        EmployeeId = longShiftDTO.EmployeeId,
                        from = longShiftDTO.FromTime.ToString(),
                        ShiftType = longShiftDTO.ShiftType,
                        //to = longShiftDTO.ToTime.ToString(),

                    };
                    if (requestShiftDTO.ShiftType == null || requestShiftDTO.ShiftType == 0)
                    {
                        requestShiftDTO.to = longShiftDTO.ToTime.ToString();
                    }
                    else
                    {
                        var ShiftType = await SettingsService.GetAllShiftTypeByIdNormal((int)(requestShiftDTO.ShiftType));
                        var time = Convert.ToDateTime(longShiftDTO.FromTime).AddHours((int)ShiftType.Hours);
                        requestShiftDTO.to = time.TimeOfDay.ToString();

                    }
                    requestShiftList.Add(requestShiftDTO);
                }
            }
            return requestShiftList;
        }
        private async Task<Shift> FillShiftObj(RequestShiftDTO requestShiftList, string FromTime)
        {
            var format = "dd/MM/yyyy";
            var formatInfo = new DateTimeFormatInfo()
            {
                ShortDatePattern = format
            };
            Shift shift = new Shift
            {
                CreatedBy = requestShiftList.CreatedBy,
                Creation_Date = DateTime.Now,
                DayDate = Convert.ToDateTime(requestShiftList.dateTimeNow, formatInfo),
                EmployeeId = requestShiftList.EmployeeId,
                From = Convert.ToDateTime(requestShiftList.from).TimeOfDay,
                Isapproved = false,
                isDeleted = false,
                LastEditeBy = requestShiftList.CreatedBy,
                LastEiteDate = DateTime.Now,
                State = (int)AllEnumes.State.Available
            };
            if (requestShiftList.ShiftType == null || requestShiftList.ShiftType == 0)
            {
                shift.to = Convert.ToDateTime(requestShiftList.to).TimeOfDay;
            }
            else
            {
                var ShiftType = await SettingsService.GetAllShiftTypeByIdNormal((int)(requestShiftList.ShiftType));
                shift.to = Convert.ToDateTime(FromTime).AddHours((int)ShiftType.Hours).TimeOfDay;
            }
            return shift;
        }
        private async Task<ShiftDTO> FillshiftdtoObj(Shift AddedShift, RequestShiftDTO requestShiftList, LongShiftDTO longShiftDTO)
        {
            ShiftDTO shiftDTO = new ShiftDTO
            {
                dateTimeNow = DateTime.Now,
                from = Convert.ToDateTime(AddedShift.From.ToString()),
                Status = (int)AllEnumes.State.Available,
                ShiftId = AddedShift.Id,
                DayDate = AddedShift.DayDate,
                EmployeeId = AddedShift.EmployeeId
            };
            if (requestShiftList.ShiftType == null || requestShiftList.ShiftType == 0)
            {
                shiftDTO.to = Convert.ToDateTime(requestShiftList.to);
            }
            else
            {
                var ShiftType = await SettingsService.GetAllShiftTypeByIdNormal((int)(requestShiftList.ShiftType));
                shiftDTO.to = Convert.ToDateTime(longShiftDTO.FromTime).AddHours((int)ShiftType.Hours);
            }
            return shiftDTO;
        }
           
        public async Task<ResultfShiftStateDTO> ResultfShiftState(checkIfShiftIsExcistDTO checkIf)
        {
            ResultfShiftStateDTO stateDTO = new ResultfShiftStateDTO();
          bool IsExcist = await  CheckIfShiftIsExcist(checkIf);
          bool InUse = await  checkIfShiftInUse(checkIf.shiftId);
            if (IsExcist&&InUse)
            {
                stateDTO.Swable = true;
                stateDTO.deletable = false;
                stateDTO.Editable = false;

            }
            if (IsExcist && !InUse)
            {
                stateDTO.Swable = true;
                stateDTO.deletable = true;
                stateDTO.Editable = true;
            }
            return stateDTO;    
        }
 
        public async Task<Slot[]> checkIfShiftInUse(Shift Shift)
        {
            var result = await _unitOfWork.SlotManger.GetAllQurAsync()
                              .Where(p => (p.Status == (int)AllEnumes.State.reserved ||
                              p.Status == (int)AllEnumes.State.Done) &&
                              p.ShiftId == Shift.Id)
                              .ToArrayAsync();
            return result;
        }

        public async Task<bool> checkIfShiftInUse(int ShiftId)
        {
            var result = await _unitOfWork.SlotManger.GetAllQurAsync()
                              .Where(p => (p.Status == (int)AllEnumes.State.reserved ||
                              p.Status == (int)AllEnumes.State.Done) &&
                              p.ShiftId == ShiftId)
                              .ToArrayAsync();
            return result.Any();
        }
        private async Task<Shift[]> CheckIfShiftIsExcist(RequestShiftDTO checkIfShiftIs)
        {
            var format = "dd/MM/yyyy";
            var formatInfo = new DateTimeFormatInfo()
            {
                ShortDatePattern = format
            };
            var checkIfShiftIsExcist = await _unitOfWork.ShiftManger.GetAllQurAsync()
                  .Where(x => x.EmployeeId == checkIfShiftIs.EmployeeId &&
                  x.DayDate == Convert.ToDateTime(checkIfShiftIs.dateTimeNow, formatInfo) &&
                  x.isDeleted == false)
                  .ToArrayAsync();
            return checkIfShiftIsExcist;
        }
        public async Task<bool> CheckIfShiftIsExcist(checkIfShiftIsExcistDTO checkIfShiftIs)
        {
            var format = "dd/MM/yyyy";
            var formatInfo = new DateTimeFormatInfo()
            {
                ShortDatePattern = format
            };
            bool checkIfShiftIsExcist = await _unitOfWork.ShiftManger.GetAllQurAsync()
                  .Where(x => x.EmployeeId == checkIfShiftIs.EmployeeId &&
                  x.DayDate == Convert.ToDateTime(checkIfShiftIs.ShiftDate, formatInfo) &&
                  x.isDeleted == false)
                  .AnyAsync();
            return checkIfShiftIsExcist;
        }
        public async Task<ResultDTO<Dictionary<string, dynamic>>> DeletShift(int ShiftId)
        {
            var ShiftModel = await _unitOfWork.ShiftManger.GetAllQurAsync().Where(x => x.Id == ShiftId && x.isDeleted == false).FirstOrDefaultAsync();
            if (ShiftModel != null)
            {
                ShiftModel.isDeleted = true;
            }
            bool ResultState = await _unitOfWork.ShiftManger.UpdateAsync(ShiftModel);
            var Result = new ResultDTO<Dictionary<string, dynamic>>();
            Result = new ResultDTO<Dictionary<string, dynamic>>()
            {
                State = ResultState
            };
            if (ResultState)
            {
                Result.Message = "Deleted Shift Successfuly";

            }
            else
            {
                Result.Message = "Deleted Shift not done";
            }
            return Result;
        }

    }
   
}
