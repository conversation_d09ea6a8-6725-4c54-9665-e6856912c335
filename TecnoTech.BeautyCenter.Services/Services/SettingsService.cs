using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.IO.Pipelines;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TecnoTech.BeautyCenter.DAL.SqlServer.Models;
using TecnoTech.BeautyCenter.DTOs.CoreDTO;
using TecnoTech.BeautyCenter.DTOs.DTOModel;
using TecnoTech.BeautyCenter.UnitOfWork.unitOfWork;
using static TecnoTech.BeautyCenter.DAL.SqlServer.EnumsModel.AllEnumes;

namespace TecnoTech.BeautyCenter.Services.Services
{
    public class SettingsService : ISettingsService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IConfiguration _Configure;

        public SettingsService(IUnitOfWork unitOfWork, IConfiguration configure)
        {
            _unitOfWork = unitOfWork;
            _Configure = configure;
        }


        public async Task<ResultDTO<Dictionary<string, dynamic>>> AutoAprrovable(bool State)
        {
            try
            {
                var res = new Dictionary<string, dynamic>();
                var Result = new ResultDTO<Dictionary<string, dynamic>>();
                Settings settings = await _unitOfWork.SettingsManger.GetAllQurAsync()
                    .Where(s => s.SettingName.Contains("AutoApprove"))
                    .FirstOrDefaultAsync();
                if (settings == null)
                {
                    settings = new Settings()
                    {
                        SettingName = "AutoApprove",
                        SettingValue = State.ToString(),
                        SettingsType = "Bool",
                        isDeleted = false,
                    };
                    var result = await _unitOfWork.SettingsManger.AddAsync(settings);
                }
                else
                {
                    settings.SettingValue = State.ToString().ToUpper();
                    var result = await _unitOfWork.SettingsManger.UpdateAsync(settings);

                }
                res.Add("Message", "Updated");
                Result = new ResultDTO<Dictionary<string, dynamic>>()
                {
                    Message = "Updated",
                    Object = res,
                    State = true
                };
                return Result;
            }
            catch (Exception ex)
            {
                throw ex.InnerException;
            }
        }
        public async Task<bool> IsAutoAprrovable()
        {
            Settings settings = await _unitOfWork.SettingsManger.GetAllQurAsync()
                    .Where(s => s.SettingName.Contains("AutoApprove"))
                    .FirstOrDefaultAsync();
            bool IsAutoAprrovable;
            if (settings == null)
            {
                IsAutoAprrovable = false;
            }
            else
            {
                IsAutoAprrovable = settings.SettingValue.Contains("TRUE");
            }
            return IsAutoAprrovable;
        }

        public async Task<ResultDTO<Dictionary<string, dynamic>>> AddShiftType(AddShiftTypeDTO shiftTypeDTO)
        {
          
            var res = new Dictionary<string, dynamic>();
            var Result = new ResultDTO<Dictionary<string, dynamic>>();
            try
            {
                ShiftType shiftType = new ShiftType()
                {
                    Hours = shiftTypeDTO.Hours,
                    ShiftTypeName = shiftTypeDTO.ShiftTypeName
                };
                var added = await _unitOfWork.ShiftTypeManger.AddAsync(shiftType);
                if (added != null)
                {
                    res.Add("Message", "Updated");
                    Result = new ResultDTO<Dictionary<string, dynamic>>()
                    {
                        Message = "Updated",
                        Object = res,
                        State = true
                    };
                    return Result;
                }
                else
                {
                    res.Add("Message", "faild");
                    Result = new ResultDTO<Dictionary<string, dynamic>>()
                    {
                        Message = "faild",
                        Object = res,
                        State = false
                    };
                    return Result;
                }
            }
            catch (Exception ex)
            {
                res.Add("Message", ex.Message);
                Result = new ResultDTO<Dictionary<string, dynamic>>()
                {
                    Message = "faild",
                    Object = res,
                    State = false
                };
                return Result;
                throw;
            }
        }
        public async Task<ResultDTO<Dictionary<string, dynamic>>> UpdateShiftType(UpdateShiftTypeDTO shiftTypeDTO)
        {
            var res = new Dictionary<string, dynamic>();
            var Result = new ResultDTO<Dictionary<string, dynamic>>();
            if (shiftTypeDTO.ShiftTypeId != 0)
            {
                ShiftType shiftType = await _unitOfWork.ShiftTypeManger.GetByAsync(shiftTypeDTO.ShiftTypeId);
                shiftType.Hours = shiftTypeDTO.Hours;
                shiftType.ShiftTypeName = shiftTypeDTO.ShiftTypeName;
                var added = await _unitOfWork.ShiftTypeManger.UpdateAsync(shiftType);
                if (added)
                {
                    res.Add("Message", "Updated");
                    Result = new ResultDTO<Dictionary<string, dynamic>>()
                    {
                        Message = "Updated",
                        Object = res,
                        State = true
                    };
                    return Result;
                }
                else
                {
                    res.Add("Message", "faild");
                    Result = new ResultDTO<Dictionary<string, dynamic>>()
                    {
                        Message = "faild",
                        Object = res,
                        State = false
                    };
                    return Result;
                }
            }
            else
            {
                res.Add("Message", "faild");
                Result = new ResultDTO<Dictionary<string, dynamic>>()
                {
                    Message = "faild",
                    Object = res,
                    State = false
                };
                return Result;
            }
        }
        public async Task<bool> DeleteShiftType(int shiftTypeId)
        {
            var res = new Dictionary<string, dynamic>();
            var Result = new ResultDTO<Dictionary<string, dynamic>>();
            if (shiftTypeId != 0)
            {
                ShiftType shiftType = await _unitOfWork.ShiftTypeManger.GetByAsync(shiftTypeId);


                shiftType.isDeleted = true;
                var added = await _unitOfWork.ShiftTypeManger.UpdateAsync(shiftType);
                if (added)
                {
                    res.Add("Message", "Updated");
                    Result = new ResultDTO<Dictionary<string, dynamic>>()
                    {
                        Message = "Updated",
                        Object = res,
                        State = true
                    };
                    return added;
                }
                else
                {
                    res.Add("Message", "faild");
                    Result = new ResultDTO<Dictionary<string, dynamic>>()
                    {
                        Message = "faild",
                        Object = res,
                        State = false
                    };
                    return added;
                }
            }
            else
            {
                res.Add("Message", "faild");
                Result = new ResultDTO<Dictionary<string, dynamic>>()
                {
                    Message = "faild",
                    Object = res,
                    State = false
                };
                return false;
            }
        }
        public async Task<ResultDTO<Dictionary<string, dynamic>>> GetAllShiftType()
        {
            var res = new Dictionary<string, dynamic>();
            var Result = new ResultDTO<Dictionary<string, dynamic>>();
            var added = await _unitOfWork.ShiftTypeManger.GetAllQurAsync().Where(s => s.isDeleted == false).ToListAsync();
            res.Add("Message", added);
            Result = new ResultDTO<Dictionary<string, dynamic>>()
            {
                Message = "Done",
                Object = res,
                State = true
            };
            return Result;
        }

        public async Task<ResultDTO<Dictionary<string, dynamic>>> GetAllShiftTypeById(int ShiftTypeId)
        {
            var res = new Dictionary<string, dynamic>();
            var Result = new ResultDTO<Dictionary<string, dynamic>>();
            var added = await _unitOfWork.ShiftTypeManger.GetAllQurAsync().Where(s => s.isDeleted == false && s.Id == ShiftTypeId).FirstOrDefaultAsync();
            res.Add("Message", added);
            Result = new ResultDTO<Dictionary<string, dynamic>>()
            {
                Message = "Done",
                Object = res,
                State = true
            };
            return Result;
        } 
        public async Task<ShiftType> GetAllShiftTypeByIdNormal(int ShiftTypeId)
        {
            var res = new Dictionary<string, dynamic>();
            var Result = new ResultDTO<Dictionary<string, dynamic>>();
            var added = await _unitOfWork.ShiftTypeManger.GetAllQurAsync().Where(s => s.isDeleted == false && s.Id == ShiftTypeId).FirstOrDefaultAsync();
            res.Add("Message", added);
            Result = new ResultDTO<Dictionary<string, dynamic>>()
            {
                Message = "Done",
                Object = res,
                State = true
            };
            return added;
        }


    }
}
