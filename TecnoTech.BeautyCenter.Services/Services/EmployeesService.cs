using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TecnoTech.BeautyCenter.DAL.SqlServer.EnumsModel;
using TecnoTech.BeautyCenter.DAL.SqlServer.Migrations;
using TecnoTech.BeautyCenter.DAL.SqlServer.Models;
using TecnoTech.BeautyCenter.DTOs.CoreDTO;
using TecnoTech.BeautyCenter.DTOs.DTOModel;
using TecnoTech.BeautyCenter.UnitOfWork.unitOfWork;
using static System.Reflection.Metadata.BlobBuilder;

namespace TecnoTech.BeautyCenter.Services.Services
{
    public class EmployeesService : IEmployeesService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<EmployeesService> _logger;
        public EmployeesService(IUnitOfWork unitOfWork, ILogger<EmployeesService> logger)
        {
            _logger = logger;
            _unitOfWork = unitOfWork;           
        }

        public async Task<ResultDTO<Dictionary<string, dynamic>>> CheckChangeShift(ShiftEmpDTO entity)
        {
            var Message = "";
            var res = new Dictionary<string, dynamic>();
            var From = TimeSpan.Parse(string.Format("{0:HH:mm:ss}", entity.from));
            var To = TimeSpan.Parse(string.Format("{0:HH:mm:ss}", entity.to));
            var Shiftlist = await _unitOfWork.ShiftManger.GetAllQurAsync()
                .Where(x => x.From == From && x.to == To && x.DayDate==entity.DayDate && x.isDeleted == false)
                .ToListAsync();
            if (Shiftlist.Count > 0)
            {
                Message = "Cant Change Shift , Used From Another";
                res.Add("EmployeesShift", entity);
            }
            else
            {
                Message = "Can Change Shift";
                res.Add("EmployeesShift", entity);
            }
            var Result = new ResultDTO<Dictionary<string, dynamic>>()
            {
                Message = Message,
                Object = res,
                State = Shiftlist.Count > 0 ? false : true
            };
            _logger.LogInformation(Result.Message);
            return Result;
        }

        public async Task<ResultDTO<Dictionary<string, dynamic>>> CheckSwapEmployeeShift(ShiftEmpDTO entity)
        {
            var Message = "";
            var res = new Dictionary<string, dynamic>();
            var From = TimeSpan.Parse(string.Format("{0:HH:mm:ss}", entity.from));
            var To = TimeSpan.Parse(string.Format("{0:HH:mm:ss}", entity.to));
            var Shiftlist = await _unitOfWork.ShiftManger.GetAllQurAsync().Where(x => x.From == From && x.to == To && x.EmployeeId==entity.EmployeeId && x.isDeleted == false).ToListAsync();
            if (Shiftlist.Count > 0)
            {
                Message = "Cant Swap Shift , Used From Another";
                res.Add("EmployeesShift", entity);
            }
            else
            {
                Message = "Can Swap Shift";
                res.Add("EmployeesShift", entity);
            }
            var Result = new ResultDTO<Dictionary<string, dynamic>>()
            {
                Message = Message,
                Object = res,
                State = Shiftlist.Count > 0 ? false : true
            };
            _logger.LogInformation(Result.Message);
            return Result;
        }

        public async Task<ResultDTO<Dictionary<string, dynamic>>> GetAllEmployeesShifts()
        {
            EmpDataDTO empData = new EmpDataDTO();            
            var listEmp = new List<dynamic>();
            var Shiftlist = await _unitOfWork.applicationUserManger.GetAllQurAsync()
                .Where(r=>r.UserType==(int)AllEnumes.UserType.Employee && r.isDeleted == false)
                .Include(x => x.shifts)
                .ThenInclude(s => s.slots)
                .Include(w => w.EmpServ)
                .Select(L=>new ShiftServicesEmpVM { 
                    EmployeeId=L.Id,
                    EmployeeName=L.UserName,
                    PhoneNumber =L.PhoneNumber,
                    Services = L.EmpServ.Select(x => new ServiceVM
                    {
                        englishName = x.Service.englishName,
                    }).ToList()                 
                }).ToListAsync();          
            var res = new Dictionary<string, dynamic>();
            res.Add("AllEmployeesShifts", Shiftlist);
            var Result = new ResultDTO<Dictionary<string, dynamic>>()
            {
                Message = "Employees Shift",
                Object = res,
                State = true
            };
            _logger.LogInformation(Result.Message);
            return Result;
        }

        public async Task<ResultDTO<Dictionary<string, dynamic>>> GetEmployeeByServiceId(int requestId)
        {
            var res = new Dictionary<string, dynamic>();
            var listEmp = new List<dynamic>();
           
            var list =  _unitOfWork.EmpServManger.GetAllQurAsync().Where(x => x.ServiceId == requestId && x.isDeleted == false).ToList();
            for (int i = 0; i < list.Count; i++)
            {
                EmpDataDTO empData = new EmpDataDTO();
                var Emp =  _unitOfWork.applicationUserManger
                    .GetAllQurAsync()
                    .Where(x => x.Id == list[i].EmployeeId&& x.UserType == (int)AllEnumes.UserType.Employee && x.isDeleted == false)
                    .FirstOrDefault();
                if (Emp != null) {
                    empData.EmployeeId = Emp.Id;
                    empData.UserName = Emp.UserName;
                    empData.Image = Emp.Image;
                    listEmp.Add(empData);
                }
               
            }
            var Model = await _unitOfWork.GeneralPhotosManger
                .GetAllQurAsync()
                .Where(res => res.ReferenceKey == "BgEmployees" && res.isDeleted == false)
                .ToListAsync();
            var IsExist = listEmp.Any(res => res.UserName.Contains("Emad Bazzi") || res.EmployeeId.Contains("1e2a95b6-8f4d-438a-b2b1-1cf1144aa41a"));
            if (IsExist)
            {
                var index = listEmp.FindIndex(x => x.UserName == "Emad Bazzi" || x.EmployeeId== "1e2a95b6-8f4d-438a-b2b1-1cf1144aa41a");
                var item = listEmp[index];
                listEmp[index] = listEmp[0];
                listEmp[0] = item;
            }

            res.Add("backgroundphoto", Model);
            res.Add("EmployeesData", listEmp);
            var Result = new ResultDTO<Dictionary<string, dynamic>>()
            {
                Message = "All Employee",
                Object = res , 
                State =true
            };
            _logger.LogInformation(Result.Message);
            return Result;
        }

        public async Task<ResultDTO<Dictionary<string, dynamic>>> GetEmployeeShift(string requestId)
        {
            EmpDataDTO empData = new EmpDataDTO();
            var res = new Dictionary<string, dynamic>();
            var listEmp = new List<dynamic>();
            var Shiftlist = await _unitOfWork.ShiftManger.GetAllQurAsync()
                .Where(x => x.EmployeeId == requestId && x.isDeleted == false)
                //.Take(60)
                .Include(x => x.slots)
                .Select(s => new GetEmployeeShiftByEmpIdDTO
                {
                    ShiftId = s.Id,
                    to = s.to,
                    From = s.From,
                    DayDate = s.DayDate,
                    State = s.State,
                    slots = s.slots.Select(w => new SmallSlotsDTO { Status = w.Status, Start = w.Start }).OrderBy(s => s.Start).ToList()
                })
                .ToListAsync();

            res.Add("EmployeesShift", Shiftlist);
            var Result = new ResultDTO<Dictionary<string, dynamic>>()
            {
                Message = "Employees Shift",
                Object = res,
                State = true
            };
            _logger.LogInformation(Result.Message);
            return Result;
        }
        public async Task<ResultDTO<Dictionary<string, dynamic>>> GetSlotsEmployeeShift(string requestId)
        {
            EmpDataDTO empData = new EmpDataDTO();
            var res = new Dictionary<string, dynamic>();
            var listEmp = new List<dynamic>();
            var Shiftlist = await _unitOfWork.ShiftManger.GetAllQurAsync().Where(x => x.EmployeeId == requestId && x.isDeleted == false).Include(x => x.slots).ToListAsync();
            var slotsList = new List<SlotDTOVM>();
            Shiftlist.ForEach(r => {
                r.slots.ToList().ForEach(s =>
                {
                    slotsList.Add(new SlotDTOVM { start = s.Start, end = s.End, employeeId = r.EmployeeId, Id = s.Id,slotsDate=s.SlotsDate,Status=s.Status });
                });
                });
            res.Add("slotsList", slotsList);
            var Result = new ResultDTO<Dictionary<string, dynamic>>()
            {
                Message = "Employees slotsList",
                Object = res,
                State = true
            };
            _logger.LogInformation(Result.Message);
            return Result;
        }

        public async Task<ResultDTO<Dictionary<string, dynamic>>> UpdateEmployeeShift(ShiftDTO entity)
        {
            var Message = "";
            var res = new Dictionary<string, dynamic>();
            var From = TimeSpan.Parse(string.Format("{0:HH:mm:ss}", entity.from));
            var To = TimeSpan.Parse(string.Format("{0:HH:mm:ss}", entity.to));
           
            //var Shiftlist = await _unitOfWork.ShiftManger.GetAllQurAsync().Where(x => ((x.From==From&& x.to==To&&x.DayDate==entity.DayDate)||x.Id==entity.ShiftId )&& x.isDeleted == false).ToListAsync();
            var Shiftlist = await _unitOfWork.ShiftManger.GetAllQurAsync().Where(x => ((x.From==From&& x.to==To&&x.DayDate==entity.DayDate)||x.Id==entity.ShiftId )&& x.isDeleted == false).ToListAsync();
            if (Shiftlist.Count == 0)
            {
                Message = "Can't Find this Shift";
                res.Add("EmployeesShift", entity);
            }
            else
            {
                var Shift = Shiftlist.Where(X=>X.Id==entity.ShiftId).FirstOrDefault();
                Shift.From = From;
                Shift.to = To;
                Shift.EmployeeId = entity.EmployeeId;
                Shift.DayDate = entity.DayDate;
                Shift.Creation_Date = DateTime.Now;
                Shift.State = entity.Status;
                Shift.Id = entity.ShiftId;
                var resShift = await _unitOfWork.ShiftManger.UpdateAsync(Shift);
                Message = "Updated Shift";
                res.Add("EmployeesShift", resShift);

            }
            var Result = new ResultDTO<Dictionary<string, dynamic>>()
            {
                Message = Message,
                Object = res,
                State = Shiftlist.Count == 0?false:true
            };
            _logger.LogInformation(Result.Message);
            return Result;
        }

     
    }
}
