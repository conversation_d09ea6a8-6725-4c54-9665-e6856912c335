using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TecnoTech.BeautyCenter.DAL.SqlServer.Models;
using TecnoTech.BeautyCenter.DTOs.CoreDTO;
using TecnoTech.BeautyCenter.DTOs.DTOModel;

namespace TecnoTech.BeautyCenter.Services.Services
{
    public interface ISettingsService
    {
        Task<ResultDTO<Dictionary<string, dynamic>>> AutoAprrovable(bool State);
        Task<bool> IsAutoAprrovable();
        Task<ResultDTO<Dictionary<string, dynamic>>> GetAllShiftType();
        Task<ResultDTO<Dictionary<string, dynamic>>> GetAllShiftTypeById(int ShiftTypeId);
        Task<ResultDTO<Dictionary<string, dynamic>>> AddShiftType(AddShiftTypeDTO shiftTypeDTO);
        Task<ResultDTO<Dictionary<string, dynamic>>> UpdateShiftType(UpdateShiftTypeDTO shiftTypeDTO);
        Task<bool> DeleteShiftType(int shiftTypeId);
        Task<ShiftType> GetAllShiftTypeByIdNormal(int ShiftTypeId);
    }
}
