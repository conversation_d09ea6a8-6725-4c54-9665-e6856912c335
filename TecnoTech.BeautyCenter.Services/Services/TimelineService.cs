using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TecnoTech.BeautyCenter.DAL.SqlServer.EnumsModel;
using TecnoTech.BeautyCenter.DAL.SqlServer.Models;
using TecnoTech.BeautyCenter.DTOs.DTOModel;

namespace TecnoTech.BeautyCenter.Services.Services
{
    public class TimelineService: ITimelineService
    {
        public static int SlotDurationMinutes = 10;
        public static int MorningShiftStarts = 0;
        public static int MorningShiftEnds = 24;


        public  List<Slot> GenerateSlots(ShiftDTO shiftDTO)
        {
            var result = new List<Slot>();
            var timeline = GenerateTimeline(shiftDTO.from, shiftDTO.to);

            foreach (var cell in timeline)
            {
                if (shiftDTO.from <= cell.Start && cell.End <= shiftDTO.to)
                {
                    for (var slotStart = cell.Start; slotStart < cell.End; slotStart = slotStart.AddMinutes(SlotDurationMinutes))
                    {
                        var slotEnd = slotStart.AddMinutes(SlotDurationMinutes);

                        Slot slot = new Slot()
                        {
                            Start = slotStart.TimeOfDay,
                            End = slotEnd.TimeOfDay,
                            Status = (int)AllEnumes.State.Available,
                            ShiftId = shiftDTO.ShiftId,
                            SlotsDate = shiftDTO.DayDate                         
                        };
                        result.Add(slot);
                    }
                }
            }
            return result;
        }

       private  List<TimeCellDTO> GenerateTimeline(DateTime start, DateTime end)
        {
            var result = new List<TimeCellDTO>();
            var incrementMorning = 1;
            var days = (end.Date - start.Date).TotalDays;

            if (end > end.Date)
            {
                days += 1;
            }

            for (var i = 0; i < days; i++)
            {
                var day = start.Date.AddDays(i);
                for (var x = MorningShiftStarts; x < MorningShiftEnds; x += incrementMorning)
                {
                    var cell = new TimeCellDTO();
                    cell.Start = day.AddHours(x);
                    cell.End = day.AddHours(x + incrementMorning);
                    result.Add(cell);
                }
            }
            return result.OrderBy(x => x.Start).ToList();
        }

        
    }
}
