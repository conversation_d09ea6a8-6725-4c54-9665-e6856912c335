using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TecnoTech.BeautyCenter.Services.Services
{
    public interface ICustomerService
    {
        Task<dynamic> getAllBillCompleteJobByUserId(string CustomerId);
        Task<dynamic> getAllBillCompleteJob();
        Task<dynamic> getAllCompleteJobByUserId(string CustomerId);
        Task<dynamic> getAllCompleteJob(); 
        Task<pointCashDTO> GetPoints(string CustomerId);
    }
}
