using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TecnoTech.BeautyCenter.DTOs.CoreDTO;
using TecnoTech.BeautyCenter.DTOs.DTOModel;

namespace TecnoTech.BeautyCenter.Services.Services
{
    public interface IServicesService
    {
        Task<ResultDTO<Dictionary<string, dynamic>>> GetAll();
        Task<ResultDTO<Dictionary<string, dynamic>>> GetServiceByName(string name);
        Task<ResultDTO<Dictionary<string, dynamic>>> GetAsync(int id);
        Task<ResultDTO<Dictionary<string, dynamic>>> Insert(ServicesDTO servicesDTO);
        Task<ResultDTO<Dictionary<string, dynamic>>> Update(ServiceEditDTO servicesDTO);
        Task<ResultDTO<Dictionary<string, dynamic>>> SignEmployeeInServices(string EmployeeId, List<ServicesUserDTO> servicesList);
        Task<ResultDTO<Dictionary<string, dynamic>>> GetEmployeeInServices(string EmployeeId);
        Task<ResultDTO<Dictionary<string, dynamic>>> UploadFile(UploadFileDTO uploadFileDTO);
        Task<ResultFileDTO> Display(string name, string key);
        Task<ResultDTO<Dictionary<string, dynamic>>> Delete(int id);
        Task<ResultDTO<Dictionary<string, dynamic>>> GetBackgroundPhotoByKey(string key);
        Task<ResultDTO<Dictionary<string, dynamic>>> GetBackgroundPhoto();
        Task<ResultDTO<Dictionary<string, dynamic>>> SendNotification(SendNotification notification);
        Task<ResultDTO<Dictionary<string, dynamic>>> GetRefrenceBackgroundPhoto();
       }
}
