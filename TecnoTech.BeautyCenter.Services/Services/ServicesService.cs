using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Nancy.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;
using TecnoTech.BeautyCenter.DAL.SqlServer.Models;
using TecnoTech.BeautyCenter.DTOs.CoreDTO;
using TecnoTech.BeautyCenter.DTOs.DTOModel;
using TecnoTech.BeautyCenter.UnitOfWork.unitOfWork;

namespace TecnoTech.BeautyCenter.Services.Services
{
    public class ServicesService : IServicesService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IGeneralService _GeneralService;
        private readonly ILogger<ServicesService> _logger;
        public ServicesService(IUnitOfWork unitOfWork, ILogger<ServicesService> logger, IGeneralService generalService)
        {
            _logger = logger;
            _unitOfWork = unitOfWork;
            _GeneralService = generalService;
        }

        public async Task<ResultDTO<Dictionary<string, dynamic>>> GetAll()
        {
            var res = new Dictionary<string, dynamic>();

            var list = await _unitOfWork.serviceManger.GetAllQurAsync().Where(r => r.isDeleted == false).ToListAsync();
            var Model = await _unitOfWork.GeneralPhotosManger.GetAllQurAsync().Where(res => res.ReferenceKey == "BgService" && res.isDeleted == false).ToListAsync();

            res.Add("backgroundphoto", Model);
            res.Add("servicesData", list);
            var Obj = new ResultDTO<Dictionary<string, dynamic>>()
            {
                Object = res,
                Message = "Done",
                State = true
            };
            return Obj;
        }

        public async Task<ResultDTO<Dictionary<string, dynamic>>> GetAsync(int id)
        {
            var res = new Dictionary<string, dynamic>();
            var service = await _unitOfWork.serviceManger.GetAllQurAsync().Where(r=>r.Id==id && r.isDeleted == false).FirstOrDefaultAsync();
            res.Add("serviceData", service);
            var Obj = new ResultDTO<Dictionary<string, dynamic>>()
            {
                Object = res,
                Message = "Done",
                State = true
            };
            return Obj;
        }

        public async Task<ResultDTO<Dictionary<string, dynamic>>> GetEmployeeInServices(string EmployeeId)
        {
            var res = new Dictionary<string, dynamic>();
            var EmployeeServiceList =await _unitOfWork.serviceManger.GetAllQurAsync().Where(r=>r.isDeleted==false).Include(x => x.EmpServ).Select(l => new UserServicesResponceDTO
            {
                ServicesId = l.Id,
                arabicName = l.arabicName,
                averageServiceTimeMin = l.averageServiceTimeMin,
                englishName = l.englishName,
                description = l.description,
                Price = l.Price,
                //IsSelected = EmployeeId == l.EmpServ.FirstOrDefault().EmployeeId ? true : false
                IsSelected = l.EmpServ.Where(e=>e.EmployeeId==EmployeeId && e.isDeleted == false).FirstOrDefault()!=null ? true : false
            }).ToListAsync();
            if (EmployeeServiceList != null || EmployeeServiceList.Count !=0)
            {
                res.Add("EmployeeServiceList", EmployeeServiceList);
                var Obj = new ResultDTO<Dictionary<string, dynamic>>()
                {
                    Object = res,
                    Message = "Done",
                    State = true
                };
                return Obj;
            }
            else
            {
                res.Add("EmployeeServiceList", null);
                var Obj = new ResultDTO<Dictionary<string, dynamic>>()
                {
                    Object = res,
                    Message = "Not Done",
                    State = false
                };
                return Obj;
            }
        }

        public async Task<ResultDTO<Dictionary<string, dynamic>>> GetServiceByName(string name)
        {
            var res = new Dictionary<string, dynamic>();
            var list =await _unitOfWork.serviceManger.GetAllAsyn();
            var serviceslist = new List<ServiceVM>();
            foreach (var item in list)
            {
                var enName = item.englishName.ToLower().Contains(name.ToLower());
                var arName = item.arabicName.ToLower().Contains(name.ToLower());
                if (enName == true || arName == true)
                {
                    serviceslist.Add(new ServiceVM { Id = item.Id, description = item.description, arabicName = item.arabicName, englishName = item.englishName,Icon=item.Icon });
                }
            }
            res.Add("serviceData", serviceslist);
            var Obj = new ResultDTO<Dictionary<string, dynamic>>()
            {
                Object = res,
                Message = "Done",
                State = true
            };
            return Obj;
        }

        public async Task<ResultDTO<Dictionary<string, dynamic>>> Insert(ServicesDTO servicesDTO)
        {
            var res = new Dictionary<string, dynamic>();
            Service service = new Service()
            {
                arabicName = servicesDTO.arabicName,
                description = servicesDTO.description,
                englishName = servicesDTO.englishName,
                averageServiceTimeMin = servicesDTO.averageServiceTimeMin == null ? 0 : (int)servicesDTO.averageServiceTimeMin,
                Price = servicesDTO.Price == null ? 0 : (double)servicesDTO.Price,
                Points = servicesDTO.Points == null ? 0 : (int)servicesDTO.Points,
                CreatedBy = servicesDTO.CreatedBy,
                Creation_Date = DateTime.Now,
                isDeleted = false,
                LastEditeBy = servicesDTO.CreatedBy,
                LastEiteDate = DateTime.Now,
                SlotsNumber = servicesDTO.SlotsNumber,
                 Icon = servicesDTO.Icon==null ? "" : servicesDTO.Icon,   

            };

            var result = await _unitOfWork.serviceManger.AddAsync(service);
            if (result != null)
            {
                _logger.LogError("Service Name : " + service.arabicName + "  Done when creating");
                res.Add("serviceData", result);
                var Obj = new ResultDTO<Dictionary<string, dynamic>>()
                {
                    Object = res,
                    Message = "Done",
                    State = true
                };
                return Obj;
            }
            else
            {
                _logger.LogError("Service Name : " + service.arabicName + " Not Done when creating");
                res.Add("service Data", null);
                var Obj = new ResultDTO<Dictionary<string, dynamic>>()
                {
                    Object = res,
                    Message = "Not Done",
                    State = false
                };
                return Obj;
            }
        }

        public async Task<ResultDTO<Dictionary<string, dynamic>>> Update(ServiceEditDTO servicesDTO)
        {
            var res = new Dictionary<string, dynamic>();
            var service = _unitOfWork.serviceManger.GetBy(servicesDTO.Id);
            service.arabicName = servicesDTO.arabicName;
            service.description = servicesDTO.description;
            service.englishName = servicesDTO.englishName;
            service.averageServiceTimeMin = servicesDTO.averageServiceTimeMin != null ? 0 : (int)servicesDTO.averageServiceTimeMin;
            service.Price = servicesDTO.Price == null ? 0 : (double)servicesDTO.Price;
            service.LastEiteDate = DateTime.Now;
            var result = await _unitOfWork.serviceManger.UpdateAsync(service);
            if (result != null)
            {
                _logger.LogError("Service Name : " + service.arabicName + " Done when Updateing");
                res.Add("serviceData", result);
                var Obj = new ResultDTO<Dictionary<string, dynamic>>()
                {
                    Object = res,
                    Message = "Done",
                    State = true
                };
                return Obj;
            }
            else
            {
                _logger.LogError("Service Name : " + service.arabicName + "not Done when Updateing");
                res.Add("service Data", null);
                var Obj = new ResultDTO<Dictionary<string, dynamic>>()
                {
                    Object = res,
                    Message = "Not Done",
                    State = false
                };
                return Obj;
            }

        }

        public async Task<ResultDTO<Dictionary<string, dynamic>>> SignEmployeeInServices(string EmployeeId, List<ServicesUserDTO> servicesList)
        {
            var res = new Dictionary<string, dynamic>();
            List<EmpServ> EmpServList = new List<EmpServ>();
            EmpServ Model;
            for (int i = 0; i < servicesList.Count; i++)
            {
                Model = new EmpServ()
                {
                    EmployeeId = EmployeeId,
                    ServiceId = servicesList[i].ServiceId,
                    Creation_Date = DateTime.Now
                };
                EmpServList.Add(Model);
            }
            var oldPerm = _unitOfWork.EmpServManger.GetAllQurAsync().Where(r => r.EmployeeId == EmployeeId && r.isDeleted == false).ToList();
            _unitOfWork.EmpServManger.RemoveRange(oldPerm);
            var result =await _unitOfWork.EmpServManger.AddRengAsync(EmpServList);
            if (result)
            {
                res.Add("serviceData", result);
                var Obj = new ResultDTO<Dictionary<string, dynamic>>()
                {
                    Object = res,
                    Message = "Done",
                    State = true
                };
                return Obj;
            }
            else
            {
                res.Add("service Data", null);
                var Obj = new ResultDTO<Dictionary<string, dynamic>>()
                {
                    Object = res,
                    Message = "Not Done",
                    State = false
                };
                return Obj;
            }

        }

        public async Task<ResultDTO<Dictionary<string, dynamic>>> UploadFile(UploadFileDTO uploadFileDTO )
        {
            var resD = new Dictionary<string, dynamic>();
            var Obj = new ResultDTO<Dictionary<string, dynamic>>();
            var ReferenceKey = uploadFileDTO.ReferenceKey ;
            var ReferencePath = uploadFileDTO.ReferencePath ;
            var ReferenceId = uploadFileDTO.ReferenceId ;
            var CreatedBy = uploadFileDTO.CreatedBy ;
            IFormFile files = uploadFileDTO.files.FirstOrDefault();
            var uploadDirecotroy = uploadFileDTO.uploadDirecotroy;
            if (files == null)
            {
                Obj = new ResultDTO<Dictionary<string, dynamic>>()
                {
                    Object = null,
                    Message = "Image not selected",
                    State = false
                };
                return Obj;                
            }            
            uploadDirecotroy = _GeneralService.getPath(ReferencePath);
            var imageName = Path.GetFileNameWithoutExtension(files.FileName);
            var fileName = imageName + DateTime.Now.Ticks.ToString() + Path.GetExtension(files.FileName);
            var path = Path.Combine(uploadDirecotroy, fileName);
            //var fileName = ContentDispositionHeaderValue.Parse(files[0].ContentDisposition).FileName.Trim('"');
            var FileExtension = Path.GetExtension(fileName);
            //fileName = Path.Combine(uploadDirecotroy, fileName);
            using (var stream = new FileStream(path, FileMode.Create))
            {
                await files.CopyToAsync(stream);
            }
            //using (FileStream fs = System.IO.File.Create(fileName))
            //{
            //    files[0].CopyTo(fs);
            //    fs.Flush();
            //}
            if (files.Length > 150000)
            {
               _GeneralService.ResizeAndSaveImage(files.OpenReadStream(), fileName);
            }
            var Result = await _GeneralService.saveImageData(ReferenceId, ReferencePath, ReferenceKey, fileName, files.ContentType, CreatedBy);
            if (Result!=null)
            {
                string Message = "Image Or Icon Saved !";
                _logger.LogInformation(Message);
                resD.Add("Result", Result);
                Obj = new ResultDTO<Dictionary<string, dynamic>>()
                {
                    Object = resD,
                    Message = Message,
                    State = true
                };
                return  Obj;
            }
            else
            {
                string Message = "Image Or Icon Not Saved !";
                _logger.LogError(Message);
                resD.Add("error", Message);
                Obj = new ResultDTO<Dictionary<string, dynamic>>()
                {
                    Object = resD,
                    Message = Message,
                    State = false
                };
                return Obj;
            }
        }

        public async Task<ResultFileDTO> Display(string name, string key)
        {
            var Obj = new ResultFileDTO();
            if (name == null)
            {
                Obj = new ResultFileDTO()
                {
                    contentType = null,
                    fileContents = null,
                    Message = "name not present",
                    State = false
                };
                return Obj;
            }
            var FileExtension = Path.GetExtension(name);
            var path = Path.Combine("C:/uploads/" + key + "/", name);
            string filepath = Path.Combine(path);
            byte[] imageArry = System.IO.File.ReadAllBytes(filepath.Replace("//", "\\"));
            string base64ImageRepresantation = Convert.ToBase64String(imageArry);
            byte[] Picture = Convert.FromBase64String(base64ImageRepresantation);
            dynamic type = "image/" + FileExtension.Split('.')[1];
            string Message = "name present";
            _logger.LogInformation(Message);
            Obj = new ResultFileDTO()
            {
                contentType = type,
                fileContents = Picture,
                Message = Message,
                State = true
            };
            return Obj;
        }

        public async Task<ResultDTO<Dictionary<string, dynamic>>> Delete(int id)
        {
            var Model =await _unitOfWork.serviceManger.GetByAsync(id);
            var Obj = new ResultDTO<Dictionary<string, dynamic>>();
            var res = new Dictionary<string, dynamic>();
            string Message;
            if (Model!=null)
            {
                Message = "Service Deleted!";
                Model.isDeleted = true;
                var result = await _unitOfWork.serviceManger.UpdateAsync(Model);
                res.Add("Message", result);
                _logger.LogInformation(Message);
                Obj = new ResultDTO<Dictionary<string, dynamic>>()
                {
                    Object = res,
                    Message = Message,
                    State = true
                };
            }
            else
            {
                Message = "Service Not Found!";
                res.Add("Message", Message);
                _logger.LogError(Message);
                Obj = new ResultDTO<Dictionary<string, dynamic>>()
                {
                    Object = res,
                    Message = Message,
                    State = false
                };
            }
            return Obj;
        }

        public async Task<ResultDTO<Dictionary<string, dynamic>>> GetBackgroundPhotoByKey(string key)
        {
            var result = await _unitOfWork.GeneralPhotosManger.GetAllQurAsync().Where(res => res.ReferenceKey == key && res.isDeleted == false).ToListAsync();
            var Obj = new ResultDTO<Dictionary<string, dynamic>>();
            var res = new Dictionary<string, dynamic>();
            res.Add("BackgroundPhoto", result);
            Obj = new ResultDTO<Dictionary<string, dynamic>>()
            {
                Object = res,
                Message = "Done",
                State = true
            };
            return Obj;
        }

        public async Task<ResultDTO<Dictionary<string, dynamic>>> GetBackgroundPhoto()
        {
            var result =await _unitOfWork.GeneralPhotosManger.GetAllQurAsync().Where(r => r.isDeleted == false).ToListAsync();
            var Obj = new ResultDTO<Dictionary<string, dynamic>>();
            var res = new Dictionary<string, dynamic>();
            res.Add("BackgroundPhoto", result);
            Obj = new ResultDTO<Dictionary<string, dynamic>>()
            {
                Object = res,
                Message = "Done",
                State = true
            };
            return Obj;
           
        }

        public async Task<ResultDTO<Dictionary<string, dynamic>>> GetRefrenceBackgroundPhoto()
        {
            var list = new List<PhotoBackground>();
            var result =await _unitOfWork.GeneralPhotosManger.GetAllQurAsync().Where(r => r.isDeleted == false).ToListAsync();
            result.ForEach(r => {
                if(r.ReferenceKey.Contains("Bg"))   
                list.Add(new PhotoBackground { ReferenceKey = r.ReferenceKey, ReferenceName = "Background " + r.ReferenceKey.Split('g')[1] });

            });
            var Obj = new ResultDTO<Dictionary<string, dynamic>>();
            var res = new Dictionary<string, dynamic>();
            res.Add("BackgroundPhoto", list);
            Obj = new ResultDTO<Dictionary<string, dynamic>>()
            {
                Object = res,
                Message = "Done",
                State = true
            };
            return Obj;
           
        }

        public async Task<ResultDTO<Dictionary<string,dynamic>>> SendNotification(SendNotification notification)
        {
            var Obj = new ResultDTO<Dictionary<string, dynamic>>();
            var res = new Dictionary<string, dynamic>();
            _logger.LogError("FirebaseToken", notification.FirebaseToken);
            if (notification.FirebaseToken.Count == 0)
            {
                notification.FirebaseToken.Add("/topics/All");
            }
            if (notification.FirebaseToken == null || notification.FirebaseToken[0] == "" || notification.FirebaseToken[0] == "string")
            {
                notification.FirebaseToken[0] = "/topics/All";
            }
            //if (notification.FirebaseToken == null || notification.FirebaseToken == "" || notification.FirebaseToken == "string")
            //{
            //    notification.FirebaseToken = "/topics/All";
            //}
            var notificationDTO = new NotificationDTO()
            {
                title =  notification.title,
                body = notification.body,
                content_available = "true",
                sound = "beep",
              //  click_action = "FLUTTER_NOTIFICATION_CLICK",
                image = notification.image,
                //icon=notification.image
            };
            var result = "-1";
            var ExpData = new NotificationTextDTO()
            {
                bodyText = notification.body,
                image = notification.image,
                //icon= notification.image
            };
            //  _logger.LogError(notification.FirebaseToken);
            foreach (var item in notification.FirebaseToken)
            {
                NotificationDataDTO data = new NotificationDataDTO()
                {
                    //to = notification.FirebaseToken,
                    to = item,
                    ttl = 3600,
                    notification = notificationDTO,
                    data = ExpData
                };
                var httpWebRequest = (HttpWebRequest)WebRequest.Create("https://fcm.googleapis.com/fcm/send");
                httpWebRequest.ContentType = "application/json";
                httpWebRequest.Headers.Add(string.Format("Authorization: key={0}", notification.ServerKey));
                httpWebRequest.Headers.Add(string.Format("Sender: id={0}", notification.SenderId));
                httpWebRequest.Method = "POST";
                var serializer = new JavaScriptSerializer();
                await using (var streamWriter = new StreamWriter(httpWebRequest.GetRequestStream()))
                {
                    var json = serializer.Serialize(data);
                    streamWriter.Write(json);
                    streamWriter.Flush();
                }
                var httpResponse = (HttpWebResponse)httpWebRequest.GetResponse();
                using (var streamReader = new StreamReader(httpResponse.GetResponseStream()))
                {
                    result = streamReader.ReadToEnd();
                }

            }
            JToken tokenNotification = JObject.Parse(result);
            res.Add("Message", result);
            Obj = new ResultDTO<Dictionary<string, dynamic>>()
            {
                Object = res,
                Message = "Done",
                State = true
            };
            return Obj;
        }



    }
}
