using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TecnoTech.BeautyCenter.DTOs.CoreDTO;
using TecnoTech.BeautyCenter.DTOs.DTOModel;

namespace TecnoTech.BeautyCenter.Services.Services
{
    public interface INotificationService
    {
        Task<ResultDTO<Dictionary<string, dynamic>>> GetAll();
        Task<ResultDTO<Dictionary<string, dynamic>>> GetNotificationById(int Id);
        Task<ResultDTO<Dictionary<string, dynamic>>> Insert(NotificationsDTO notification);
        Task<ResultDTO<Dictionary<string, dynamic>>> Update(NotificationsEditDTO notifications);
    }
}
