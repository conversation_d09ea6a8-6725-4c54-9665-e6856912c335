using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TecnoTech.BeautyCenter.DAL.SqlServer.EnumsModel;
using TecnoTech.BeautyCenter.UnitOfWork.unitOfWork;

namespace TecnoTech.BeautyCenter.Services.Services
{
    public class CustomerService : ICustomerService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<CustomerService> _logger;
        public CustomerService(IUnitOfWork unitOfWork, ILogger<CustomerService> logger)
        {
            _logger = logger;
            _unitOfWork = unitOfWork;          
        }

        public async Task<dynamic> getAllBillCompleteJobByUserId(string CustomerId)
        {
            var CompleteHistory = await _unitOfWork.applicationUserManger.GetAllQurAsync()
                .Where(q => q.UserType==(int)AllEnumes.UserType.Customer && q.Id == CustomerId && q.isDeleted == false)
                .Include(s => s.Bills).ThenInclude(w => w.billItems)
                .Select(s => s.Bills.Select(q => new { Id = q.Id, CustomerId = q.CustomerId,CustomerName =s.FirstNameEn+" "+ s.lastNameEn, Amount =q.Amount, billItems = q.billItems}))
                .ToArrayAsync();
            return CompleteHistory;            
        }
        public async Task<dynamic> getAllBillCompleteJob()
        {
            var CompleteHistory = await _unitOfWork.applicationUserManger.GetAllQurAsync()
               .Where(q => q.UserType == (int)AllEnumes.UserType.Customer && q.isDeleted == false)
               .Include(s => s.Bills).ThenInclude(w => w.billItems)
               .Select(s => s.Bills.Select(q => new { Id = q.Id, CustomerId = q.CustomerId, CustomerName = s.FirstNameEn + " " + s.lastNameEn, Amount = q.Amount, billItems = q.billItems }))
               .ToArrayAsync();
            return CompleteHistory;
        }
        public async Task<dynamic> getAllCompleteJobByUserId(string CustomerId)
        {
            var CompleteHistory = await _unitOfWork.AppointmentManger.GetAllQurAsync()
                .Where(q => q.State==(int)AllEnumes.StateAppointments.Confirmed && q.CustomerId ==CustomerId && q.isDeleted == false)   
                .Include(w => w.Customer)
                .Select(q => new { Id = q.Id, CustomerId = q.CustomerId,CustomerName =q.Customer.FirstNameEn + " "+ q.Customer.lastNameEn/* ,Amount =q.Price*/})
                .ToArrayAsync();
            return CompleteHistory;            
        }
        public async Task<dynamic> getAllCompleteJob()
        {
            var CompleteHistory = await _unitOfWork.AppointmentManger.GetAllQurAsync()
                .Where(q => q.State == (int)AllEnumes.StateAppointments.Confirmed && q.isDeleted == false && q.isDeleted == false)
                .Include(w => w.Customer)
                .Select(q => new { Id = q.Id, CustomerId = q.CustomerId, CustomerName = q.Customer.FirstNameEn + " " + q.Customer.lastNameEn /*, Amount = q.Price*/ })
                .ToArrayAsync();
            return CompleteHistory;
        }

        public async Task<pointCashDTO> GetPoints(string CustomerId)
        {
            var Customer =await _unitOfWork.applicationUserManger.GetByAsync(CustomerId);

            return new pointCashDTO { Points  =  Customer.Points , Cash = 0};
        }
    }
    public class pointCashDTO
    {
        public int Points { get; set; }
        public double Cash { get; set; }

    }
}
