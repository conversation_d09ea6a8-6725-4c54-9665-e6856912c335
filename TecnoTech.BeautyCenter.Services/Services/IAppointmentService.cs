using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TecnoTech.BeautyCenter.DTOs.CoreDTO;
using TecnoTech.BeautyCenter.DTOs.DTOModel;

namespace TecnoTech.BeautyCenter.Services.Services
{
    public interface IAppointmentService
    {
        Task<ResultDTO<Dictionary<string, dynamic>>> GetAppointmentsByUserId(string userId);
        Task<ResultDTO<Dictionary<string, dynamic>>> GetPendingAppointmentsByUserId(string userId);
        Task<ResultDTO<Dictionary<string, dynamic>>> GetConfirmedAppointmentsByUserId(string userId);
        Task<ResultDTO<BookingRequestDTO>> GetAppointmentsById(int AppointmentId);
        Task<ResultDTO<Dictionary<string, dynamic>>> BookingAppointment(BookingDTO Model);
        Task<ResultDTO<List<AppointmentToApproveDTO>>> GetAppointmentToApprove(string userId);
        Task<ResultDTO<List<AppointmentToApproveDTO>>> GetConfirmidAppointment(string userId);
        Task<ResultDTO<Dictionary<string, dynamic>>> ApproveAnAppointment(int AppointmentId);

        Task<ResultDTO<Dictionary<string, dynamic>>> CancelBooking(int appointmentId);
        Task<ResultDTO<Dictionary<string, dynamic>>> CompleteBooking(int appointmentId);
        Task<ResultDTO<Dictionary<string, dynamic>>> AllBookingHistory(string userId);
        Task<ResultDTO<Dictionary<string, dynamic>>> AllBookingHistory(BookingHistorySearchFiltrationDTO filtrationDTO);
        Task<ResultDTO<Dictionary<string, dynamic>>> DeleteAppointment(int appointmentId);
        Task<ResultDTO<Dictionary<string, dynamic>>> AllBookingHistory();
        Task<ResultDTO<Dictionary<string, dynamic>>> GetAppointmentsByMobile(string Mobile);
        Task<ResultDTO<Dictionary<string, dynamic>>> AllBookingHistoryByEmployeeId(string EmployeeId);

        //Task<ResultDTO<Dictionary<string, dynamic>>> CreateShift(List<RequestShiftDTO> requestShiftList);
    }
}
