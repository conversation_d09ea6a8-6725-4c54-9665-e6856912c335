using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;
using TecnoTech.BeautyCenter.DAL.SqlServer.EnumsModel;
using TecnoTech.BeautyCenter.DAL.SqlServer.Models;
using TecnoTech.BeautyCenter.DTOs.CoreDTO;
using TecnoTech.BeautyCenter.DTOs.DTOModel;
using TecnoTech.BeautyCenter.UnitOfWork.unitOfWork;
using static TecnoTech.BeautyCenter.DAL.SqlServer.EnumsModel.AllEnumes;

namespace TecnoTech.BeautyCenter.Services.Services
{
    public class AppointmentService : IAppointmentService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<AppointmentService> _logger;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly IServicesService _ServicesService;
        private readonly ISettingsService _SettingsService;
        private readonly IConfiguration _Configure;
        public AppointmentService(IUnitOfWork unitOfWork,
            ILogger<AppointmentService> logger,
            ITimelineService timelineService,
             ISettingsService SettingsService,
            UserManager<ApplicationUser> userManager,
            IServicesService servicesService,
            IConfiguration configure)
        {
            _userManager = userManager;
            _logger = logger;
            _userManager = userManager;
            _ServicesService = servicesService;
            _Configure = configure;
            _unitOfWork = unitOfWork;
            _SettingsService = SettingsService;
        }
        public async Task<ResultDTO<Dictionary<string, dynamic>>> GetAppointmentsByUserId(string userId)
        {
            var res = new Dictionary<string, dynamic>();
            var list = await _unitOfWork.AppointmentManger.GetAllQurAsync().Where(r => r.isDeleted == false).OrderByDescending(d => d.AppointmentDate)
            .Where(r => r.CustomerId == userId).Select(e => new BookingRequestDTO
            {
                EmployeeId = e.EmployeeId,
                CustomerId = e.CustomerId,
                From = Convert.ToString(e.From),
                To = Convert.ToString(e.To),
                ServiceName = e.Service.englishName,
                AppointmentDate = e.AppointmentDate,
                AppointmentId = e.Id,
                State = e.State.ToString(),
                AnotherCustomerPhone = e.OtherCustomerPhone == null ? "" : e.OtherCustomerPhone,
                CreatedBy = e.CreatedBy
            }).ToListAsync();
            list.ForEach(r => r.State = Enum.GetName(typeof(AllEnumes.StateAppointments), int.Parse(r.State)));
            var Model = _unitOfWork.GeneralPhotosManger.GetAllQurAsync().Where(res => res.ReferenceKey == "BgAppointments" && res.isDeleted == false).ToList();

            res.Add("backgroundphoto", Model);
            res.Add("AppointmentsData", list);
            var Result = new ResultDTO<Dictionary<string, dynamic>>()
            {
                Message = "Appointments List Of User",
                State = true,
                Object = res
            };
            return Result;
        }
        public async Task<ResultDTO<Dictionary<string, dynamic>>> GetPendingAppointmentsByUserId(string userId)
        {
            var res = new Dictionary<string, dynamic>();
            var list = await _unitOfWork.AppointmentManger.GetAllQurAsync().Where(r => r.isDeleted == false).OrderByDescending(d => d.AppointmentDate)
            .Where(r => r.CustomerId == userId && r.State == (int)AllEnumes.StateAppointments.Pending).Select(e => new BookingRequestDTO
            {
                EmployeeId = e.EmployeeId,
                CustomerId = e.CustomerId,
                From = Convert.ToString(e.From),
                To = Convert.ToString(e.To),
                ServiceName = e.Service.englishName,
                AppointmentDate = e.AppointmentDate,
                AppointmentId = e.Id,
                State = e.State.ToString(),
                AnotherCustomerPhone = e.OtherCustomerPhone == null ? "" : e.OtherCustomerPhone,
                CreatedBy = e.CreatedBy
            }).ToListAsync();
            list.ForEach(r => r.State = Enum.GetName(typeof(AllEnumes.StateAppointments), int.Parse(r.State)));
            var Model = _unitOfWork.GeneralPhotosManger.GetAllQurAsync().Where(res => res.ReferenceKey == "BgAppointments" && res.isDeleted == false).ToList();

            res.Add("backgroundphoto", Model);
            res.Add("AppointmentsData", list);
            var Result = new ResultDTO<Dictionary<string, dynamic>>()
            {
                Message = "Appointments List Of User",
                State = true,
                Object = res
            };
            return Result;
        }
        public async Task<ResultDTO<Dictionary<string, dynamic>>> GetConfirmedAppointmentsByUserId(string userId)
        {
            var res = new Dictionary<string, dynamic>();
            var list = await _unitOfWork.AppointmentManger.GetAllQurAsync().Where(r => r.isDeleted == false).OrderByDescending(d => d.AppointmentDate)
            .Where(r => r.CustomerId == userId && r.State == (int)AllEnumes.StateAppointments.Confirmed).Select(e => new BookingRequestDTO
            {
                EmployeeId = e.EmployeeId,
                CustomerId = e.CustomerId,
                From = Convert.ToString(e.From),
                To = Convert.ToString(e.To),
                ServiceName = e.Service.englishName,
                AppointmentDate = e.AppointmentDate,
                AppointmentId = e.Id,
                State = e.State.ToString(),
                AnotherCustomerPhone = e.OtherCustomerPhone == null ? "" : e.OtherCustomerPhone,
                CreatedBy = e.CreatedBy
            }).ToListAsync();
            list.ForEach(r => r.State = Enum.GetName(typeof(AllEnumes.StateAppointments), int.Parse(r.State)));
            var Model = _unitOfWork.GeneralPhotosManger.GetAllQurAsync().Where(res => res.ReferenceKey == "BgAppointments" && res.isDeleted == false).ToList();

            res.Add("backgroundphoto", Model);
            res.Add("AppointmentsData", list);
            var Result = new ResultDTO<Dictionary<string, dynamic>>()
            {
                Message = "Appointments List Of User",
                State = true,
                Object = res
            };
            return Result;
        }
        public async Task<ResultDTO<BookingRequestDTO>> GetAppointmentsById(int AppointmentId)
        {
            var res = new Dictionary<string, dynamic>();
            var appointment = await _unitOfWork.AppointmentManger.GetAllQurAsync().Where(r => r.isDeleted == false).OrderByDescending(d => d.AppointmentDate)
            .Where(r => r.Id == AppointmentId).Select(e => new BookingRequestDTO
            {
                EmployeeId = e.EmployeeId,
                CustomerId = e.CustomerId,
                From = Convert.ToString(e.From),
                To = Convert.ToString(e.To),
                ServiceName = e.Service.englishName,
                AppointmentDate = e.AppointmentDate,
                AppointmentId = e.Id,
                State = e.State.ToString(),
                AnotherCustomerPhone = e.OtherCustomerPhone == null ? "" : e.OtherCustomerPhone,
                CreatedBy = e.CreatedBy,
                //Price = e.Price,
                ServiceId = e.ServiceId
            }).FirstOrDefaultAsync();
            //appointment.State = Enum.GetName(typeof(AllEnumes.StateAppointments), int.Parse(appointment.State));
            res.Add("AppointmentsData", appointment);
            var Result = new ResultDTO<BookingRequestDTO>()
            {
                Message = "Appointments List Of User",
                State = true,
                Object = appointment
            };
            return Result;
        }
        public async Task<ResultDTO<Dictionary<string, dynamic>>> BookingAppointment(BookingDTO Model)
        {
            var res = new Dictionary<string, dynamic>();
            var Result = new ResultDTO<Dictionary<string, dynamic>>();
            if (Model != null)
            {
                var CustomerDefault = _unitOfWork.applicationUserManger.GetAllQurAsync().Where(e => e.UserName == "Customer_Virtual" && e.isDeleted == false).FirstOrDefault();

                if (Model.OtherCustomerPhone == "")
                {
                    var userphone = await _unitOfWork.applicationUserManger.GetAllQurAsync().Where(e => e.Id == Model.CustomerId && e.isDeleted == false).FirstAsync();
                    if (userphone != null)
                    {
                        var PhoneNum = userphone.PhoneNumber;
                        Model.OtherCustomerPhone = PhoneNum;
                    }
                }
                var From = string.Format("{0:HH:mm:ss}", Model.from);
                var To = string.Format("{0:HH:mm:ss}", Model.to);
                var AppointmentDate1 = DateTime.Parse(Model.AppointmentDate).ToString("yyyy-MM-dd");
                //DateTime AppointmentDate1 = DateTime.ParseExact(Model.AppointmentDate, "yyyy-MM-dd", null);
                var Cust_v = Model.CustomerId == null ? CustomerDefault.Id : Model.CustomerId;
                //var IsBooking = _unitOfWork.AppointmentManger.GetAllQurAsync()
                //    .Where(re =>
                //      re.From == TimeSpan.Parse(From)
                //      && re.To == TimeSpan.Parse(To)
                //      && re.CustomerId == Cust_v
                //      && re.EmployeeId == Model.EmployeeId &&
                //      re.AppointmentDate == DateTime.Parse(AppointmentDate1)).ToList();
                var IsBooking = _unitOfWork.AppointmentManger.GetAllQurAsync()
                    .Where(re =>
                      re.isDeleted == false &&
                      re.From == TimeSpan.Parse(From)
                      && re.To == TimeSpan.Parse(To) &&
                      re.EmployeeId==Model.EmployeeId&&
                      re.AppointmentDate == DateTime.Parse(AppointmentDate1)).ToList();
                //if (IsBooking.Count > 0)
                //{
                //    res.Add("Message", "this already booked");
                //    Result = new ResultDTO<Dictionary<string, dynamic>>()
                //    {
                //        Message = "this already booked",
                //        Object = res,
                //        State = false
                //    };
                //    return Result;
                //}
                var ServicePrice = await _unitOfWork.serviceManger.GetAllQurAsync().Where(x => x.Id == Model.ServiceId && x.isDeleted == false).Select(w => w.Price).FirstOrDefaultAsync();
                bool IsAutoAprrovable = await _SettingsService.IsAutoAprrovable();
                Appointment appointment = new Appointment()
                {
                    OtherCustomerPhone = Model.OtherCustomerPhone,
                    AppointmentDate = Convert.ToDateTime(Model.AppointmentDate).Date,
                    CreatedBy = Model.CreateById,
                    From = Model.from.Value.TimeOfDay,
                    To = Model.to.Value.TimeOfDay,
                    ServiceId = Model.ServiceId,
                    CustomerId = Cust_v,
                    EmployeeId = IsBooking.Count > 0?null: Model.EmployeeId,

                    LastEiteDate = DateTime.Now,
                    LastEditeBy = Model.CreateById,
                    Creation_Date = DateTime.Now,
                    isDeleted = false,
                    //Price = ServicePrice
                };
                if (!IsAutoAprrovable)
                {
                    appointment.State = (int)AllEnumes.StateAppointments.Pending;
                }
                else
                {
                    appointment.State = (int)AllEnumes.StateAppointments.Confirmed;
                }
                var AppointmentModel = await _unitOfWork.AppointmentManger.AddAsync(appointment);
                if (AppointmentModel.Id != 0)
                {
                    var service = _unitOfWork.serviceManger.GetAll().Where(r => r.Id == AppointmentModel.ServiceId).FirstOrDefault();
                    var emp = _unitOfWork.applicationUserManger.GetBy(AppointmentModel.EmployeeId);
                    var cutomer = _unitOfWork.applicationUserManger.GetBy(AppointmentModel.CustomerId);
                    Notification notif = new Notification()
                    {
                        ServiceName = service.englishName,
                        AppointmentDate = AppointmentModel.AppointmentDate,
                        AppointmentId = AppointmentModel.Id,
                        CustomerId = AppointmentModel.CustomerId,
                        CustomerName = cutomer.UserName,
                        EmployeeId = AppointmentModel.EmployeeId,
                        EmployeeName = emp==null?null:emp.UserName,
                        From = AppointmentModel.From.ToString(),
                        To = AppointmentModel.To.ToString(),
                        Creation_Date = DateTime.Now,
                        isDeleted = false,
                        ServiceId = AppointmentModel.ServiceId,
                        CustomerPhone = AppointmentModel.OtherCustomerPhone,
                    };

                    var result = _unitOfWork.NotificationManger.Add(notif);
                }
                Slot SlotModel;
                var SlotListDB = _unitOfWork.SlotManger.GetAllQurAsync();
                for (int i = 0; i < Model.BookingModelSlotsList.Count; i++)
                {
                    SlotModel = new Slot();
                    SlotModel = SlotListDB.Where(x => x.Id == Model.BookingModelSlotsList[i]).FirstOrDefault();
                    SlotModel.Status = IsAutoAprrovable == false ? (int)AllEnumes.State.reserved : (int)AllEnumes.State.Done;
                    SlotModel.AppointmentId = AppointmentModel.Id;
                    await _unitOfWork.SlotManger.UpdateAsync(SlotModel);
                }
                res.Add("Message", "Added and appointment");
                res.Add("AppointmentId", AppointmentModel.Id);
                Result = new ResultDTO<Dictionary<string, dynamic>>()
                {
                    Message = "Added and appointment",
                    Object = res,
                    State = true
                };
                return Result;
            }
            else
            {
                Result = new ResultDTO<Dictionary<string, dynamic>>()
                {
                    Message = "Model Is not Valid",
                    Object = null,
                    State = false
                };
                return Result;
            }
        }
        public async Task<ResultDTO<List<AppointmentToApproveDTO>>> GetConfirmidAppointment(string userId)
        {
            var res = new Dictionary<string, dynamic>();
            var AppointmentList = new List<AppointmentToApproveDTO>();
            var AppointmentList2 = await _unitOfWork.AppointmentManger.GetAllQurAsync()
                       .Where(r => r.isDeleted == false && r.EmployeeId == userId).ToListAsync();
            if (userId != null)
            {


                AppointmentList = await _unitOfWork.AppointmentManger.GetAllQurAsync()
                    .Where(r => r.isDeleted == false && r.EmployeeId == userId).Include(s => s.Service)
                    .Include(x => x.Employee).Include(c => c.Customer)
                    .Select(s => new AppointmentToApproveDTO
                    {
                        From = s.From,
                        To = s.To,
                        AppointmentDate = s.AppointmentDate,
                        ServiceArabicName = s.Service.arabicName,
                        ServiceEnglishName = s.Service.englishName,
                        ServiceId = s.Service.Id,
                        CustomerName = s.Customer.FirstNameEn + " " + s.Customer.lastNameEn,
                        CustomerPhoneNumber = s.Customer.PhoneNumber,
                        CustomerEmail = s.Customer.Email,
                        CustomerId = s.Customer.Id,
                        EmployeeName = s.Employee.FirstNameEn + " " + s.Employee.lastNameEn,
                        EmployeeId = s.Employee.Id,
                        EmployeePhoneNumber = s.Employee.PhoneNumber,
                        EmployeeEmail = s.Employee.Email,
                        State = s.State,
                        Id = s.Id,
                        isDeleted = s.isDeleted
                    })
                    .Where(x => x.isDeleted == false && x.State == (int)AllEnumes.StateAppointments.Confirmed).ToListAsync();

            }
            else
            {
                AppointmentList = await _unitOfWork.AppointmentManger.GetAllQurAsync().Where(r => r.isDeleted == false).Include(s => s.Service).Include(x => x.Employee).Include(c => c.Customer).Select(s => new AppointmentToApproveDTO
                {
                    From = s.From,
                    To = s.To,
                    AppointmentDate = s.AppointmentDate,
                    ServiceArabicName = s.Service.arabicName,
                    ServiceEnglishName = s.Service.englishName,
                    ServiceId = s.Service.Id,
                    //CustomerName = s.Customer.UserName,
                    CustomerName = s.Customer.FirstNameEn + " " + s.Customer.lastNameEn,
                    CustomerPhoneNumber = s.Customer.PhoneNumber,
                    CustomerEmail = s.Customer.Email,
                    CustomerId = s.Customer.Id,
                    EmployeeName = s.Employee.FirstNameEn + " " + s.Employee.lastNameEn,
                    EmployeeId = s.Employee.Id,
                    EmployeePhoneNumber = s.Employee.PhoneNumber,
                    EmployeeEmail = s.Employee.Email,
                    State = s.State,
                    Id = s.Id,
                    isDeleted = s.isDeleted
                }).Where(x => x.isDeleted == false && x.State == (int)AllEnumes.StateAppointments.Pending).ToListAsync();

            }
            var Result = new ResultDTO<List<AppointmentToApproveDTO>>()
            {
                Message = "All AppointMent Geted",
                Object = AppointmentList,
                State = true
            };

            return Result;
        }
        public async Task<ResultDTO<List<AppointmentToApproveDTO>>> GetAppointmentToApprove(string userId)
        {
            var res = new Dictionary<string, dynamic>();
            var AppointmentList = new List<AppointmentToApproveDTO>();
            var AppointmentList2 = await _unitOfWork.AppointmentManger.GetAllQurAsync()
                       .Where(r => r.isDeleted == false && r.EmployeeId == userId).ToListAsync();
            if (userId != null)
            {


                AppointmentList = await _unitOfWork.AppointmentManger.GetAllQurAsync()
                    .Where(r => r.isDeleted == false && r.EmployeeId == userId).Include(s => s.Service)
                    .Include(x => x.Employee).Include(c => c.Customer)
                    .Select(s => new AppointmentToApproveDTO
                    {
                        From = s.From,
                        To = s.To,
                        AppointmentDate = s.AppointmentDate,
                        ServiceArabicName = s.Service.arabicName,
                        ServiceEnglishName = s.Service.englishName,
                        ServiceId = s.Service.Id,
                        CustomerName = s.Customer.FirstNameEn + " " + s.Customer.lastNameEn,
                        CustomerPhoneNumber = s.Customer.PhoneNumber,
                        CustomerEmail = s.Customer.Email,
                        CustomerId = s.Customer.Id,
                        EmployeeName = s.Employee.FirstNameEn + " " + s.Employee.lastNameEn,
                        EmployeeId = s.Employee.Id,
                        EmployeePhoneNumber = s.Employee.PhoneNumber,
                        EmployeeEmail = s.Employee.Email,
                        State = s.State,
                        Id = s.Id,
                        isDeleted = s.isDeleted,
                        Creation_Date = s.Creation_Date,

                    })
                    .Where(x => x.isDeleted == false && x.State == (int)AllEnumes.StateAppointments.Pending).ToListAsync();

            }
            else
            {
                AppointmentList = await _unitOfWork.AppointmentManger.GetAllQurAsync().Where(r => r.isDeleted == false).Include(s => s.Service).Include(x => x.Employee).Include(c => c.Customer).Select(s => new AppointmentToApproveDTO
                {
                    From = s.From,
                    To = s.To,
                    AppointmentDate = s.AppointmentDate,
                    ServiceArabicName = s.Service.arabicName,
                    ServiceEnglishName = s.Service.englishName,
                    ServiceId = s.Service.Id,
                    //CustomerName = s.Customer.UserName,
                    CustomerName = s.Customer.FirstNameEn + " " + s.Customer.lastNameEn,
                    CustomerPhoneNumber = s.Customer.PhoneNumber,
                    CustomerEmail = s.Customer.Email,
                    CustomerId = s.Customer.Id,
                    EmployeeName = s.Employee.FirstNameEn + " " + s.Employee.lastNameEn,
                    EmployeeId = s.Employee.Id,
                    EmployeePhoneNumber = s.Employee.PhoneNumber,
                    EmployeeEmail = s.Employee.Email,
                    State = s.State,
                    Id = s.Id,
                    isDeleted = s.isDeleted,
                    Creation_Date = s.Creation_Date,
                }).Where(x => x.isDeleted == false && x.State == (int)AllEnumes.StateAppointments.Pending).ToListAsync();

            }
            var Result = new ResultDTO<List<AppointmentToApproveDTO>>()
            {
                Message = "All AppointMent Geted",
                Object = AppointmentList,
                State = true
            };

            return Result;
        }
        public async Task<ResultDTO<Dictionary<string, dynamic>>> ApproveAnAppointment(int AppointmentId)
        {
            var res = new Dictionary<string, dynamic>();
            var Result = new ResultDTO<Dictionary<string, dynamic>>();
            try
            {

                if (AppointmentId != 0)
                {


                    var AppointmentModel = _unitOfWork.AppointmentManger.GetAllQurAsync().Where(r => r.isDeleted == false).Include(s => s.Service).Include(x => x.Employee).Where(x => x.isDeleted == false && x.Id == AppointmentId).FirstOrDefault();
                    if (AppointmentModel != null)
                    {
                        AppointmentModel.State = (int)AllEnumes.StateAppointments.Confirmed;
                        var ResultUpdated = await _unitOfWork.AppointmentManger.UpdateAsync(AppointmentModel);
                        var SlotModel = new Slot();
                        var SlotListDB = _unitOfWork.SlotManger.GetAllQurAsync();

                        SlotModel = SlotListDB.Where(x => x.AppointmentId == AppointmentId).FirstOrDefault();
                        SlotModel.Status = (int)AllEnumes.State.Done;
                        SlotModel.AppointmentId = AppointmentId;
                        await _unitOfWork.SlotManger.UpdateAsync(SlotModel);
                        if (Result != null)
                        {
                            res.Add("Message", "The appointment is approved");
                            Result = new ResultDTO<Dictionary<string, dynamic>>()
                            {
                                Message = "The appointment is approved",
                                State = true,
                                Object = res
                            };
                            var user = _userManager.Users.Where(r => r.Id == AppointmentModel.CustomerId).Where(r => r.isDeleted == false).FirstOrDefault();
                            List<string> FBT = new List<string>();
                            FBT.Add(user.device_token);
                            var notification = new SendNotification()
                            {
                                FirebaseToken = FBT,
                                //FirebaseToken = user.device_token,
                                body = "Your Booking is approved",
                                image = "",
                                ServerKey = _Configure["firebase:ServerKey"],
                                SenderId = _Configure["firebase:SenderId"]
                            };
                            var notifecation = _ServicesService.SendNotification(notification);
                            //return Ok(res);
                            ////return Ok("The appointment is approved ");
                        }
                        else
                        {
                            // return BadRequest("the system note an error");
                            res.Add("Errors", "the system note an error");
                            Result = new ResultDTO<Dictionary<string, dynamic>>()
                            {
                                Message = "the system note an error",
                                State = false,
                                Object = res
                            };
                        }
                    }
                    else
                    {
                        //return BadRequest("the system note an error");
                        res.Add("Errors", "the system note an error");
                        Result = new ResultDTO<Dictionary<string, dynamic>>()
                        {
                            Message = "the system note an error",
                            State = false,
                            Object = res
                        };
                    }

                }
                else
                {
                    res.Add("Errors", "the system note an error");
                    Result = new ResultDTO<Dictionary<string, dynamic>>()
                    {
                        Message = "the system note an error",
                        State = false,
                        Object = res
                    };
                }
                return Result;
            }
            catch (Exception ex)
            {
                res.Add("Errors", "the system note an error  " + ex.Message);
                Result = new ResultDTO<Dictionary<string, dynamic>>()
                {
                    Message = "the system note an error",
                    State = false,
                    Object = res
                };

                return Result;
            }
        }
        public async Task<ResultDTO<Dictionary<string, dynamic>>> CancelBooking(int appointmentId)
        {
            var res = new Dictionary<string, dynamic>();
            var Result = new ResultDTO<Dictionary<string, dynamic>>();
            var data = _unitOfWork.AppointmentManger.GetAllQurAsync().Where(re => re.Id == appointmentId && re.isDeleted == false).FirstOrDefault();

            data.State = (int)AllEnumes.StateAppointments.Canceled;
            var ResultUpdated = await _unitOfWork.AppointmentManger.UpdateAsync(data);
            var SlotModel = new Slot();
            var SlotListDB = _unitOfWork.SlotManger.GetAllQurAsync();

            SlotModel = SlotListDB.Where(x => x.AppointmentId == appointmentId).FirstOrDefault();
            SlotModel.Status = (int)AllEnumes.State.Available;
            SlotModel.AppointmentId = appointmentId;
            await _unitOfWork.SlotManger.UpdateAsync(SlotModel);
            if (ResultUpdated != null)
            {
                res.Add("Message", "Appointment cancelled ");

                Result = new ResultDTO<Dictionary<string, dynamic>>()
                {
                    Message = "Appointment cancelled",
                    State = true,
                    Object = res
                };
                var user = _userManager.Users.Where(r => r.Id == data.CustomerId && r.isDeleted == false).FirstOrDefault();
                List<string> FBT = new List<string>();
                FBT.Add(user.device_token);
                var notification = new SendNotification()
                {
                    FirebaseToken = FBT,
                    // FirebaseToken = user.device_token,

                    body = "Your Booking is cancelled",
                    image = "",
                    ServerKey = _Configure["firebase:ServerKey"],
                    SenderId = _Configure["firebase:SenderId"]
                };
                var notifecation = _ServicesService.SendNotification(notification);
                return Result;
            }
            res.Add("Errors", "the system note an error");
            Result = new ResultDTO<Dictionary<string, dynamic>>()
            {
                Message = "the system note an error",
                State = false,
                Object = res
            };

            return Result;
        }
        public async Task<ResultDTO<Dictionary<string, dynamic>>> AllBookingHistory(string userId)
        {           
            var res = new Dictionary<string, dynamic>();
            var data = new List<BookingFilttrationDTO>();
            if (userId != null)
            {
                data = await _unitOfWork.AppointmentManger.GetAllQurAsync()
                    .Where(re => re.CustomerId == userId && re.isDeleted == false).Include(s => s.Customer)
                    .Include(s => s.Service)
                    .Include(s => s.Employee)
                    .Select(s => new BookingFilttrationDTO
                    {
                        Id = s.Id,
                        From = s.From,
                        To = s.To,
                        CustomerId = s.CustomerId,
                        EmployeeId = s.EmployeeId,
                        State = s.State,
                        OtherCustomerPhone = s.OtherCustomerPhone,
                        AppointmentDate = s.AppointmentDate,
                        Creation_Date = s.Creation_Date,

                        Employee = new EmployeeFillterDto
                        {
                            Id = s.EmployeeId,
                            UserName = s.Employee.UserName,
                            FirstNameEn =s.Employee.FirstNameEn,
                            lastNameEn=s.Employee.lastNameEn,
                        },
                        Service = new ServiceFillterDto
                        {
                            arabicName = s.Service.arabicName,
                            englishName = s.Service.englishName,
                            Id = s.Service.Id
                        },
                        Customer = new CustomerFillterDto
                        {
                            Id = s.CustomerId,
                            UserName = s.Customer.UserName,
                            FirstNameEn = s.Customer.FirstNameEn,
                            lastNameEn = s.Customer.lastNameEn,
                        }
                    }
                    )
                    .ToListAsync();

            }
            else
            {
                data = await _unitOfWork.AppointmentManger.GetAllQurAsync()
                    .Where(re => re.isDeleted == false).Include(s => s.Service)
                    .Include(s => s.Employee)
                    .Select(s => new BookingFilttrationDTO
                    {
                        Id = s.Id,
                        From = s.From,
                        To = s.To,
                        CustomerId = s.CustomerId,
                        EmployeeId = s.EmployeeId,
                        State = s.State,
                        OtherCustomerPhone = s.OtherCustomerPhone,
                        AppointmentDate = s.AppointmentDate,
                        Creation_Date = s.Creation_Date,
                        Employee = new EmployeeFillterDto
                        {
                            Id = s.EmployeeId,
                            UserName = s.Employee.UserName,
                            FirstNameEn = s.Employee.FirstNameEn,
                            lastNameEn = s.Employee.lastNameEn,
                        },
                        Service = new ServiceFillterDto
                        {
                            arabicName = s.Service.arabicName,
                            englishName = s.Service.englishName,
                            Id = s.Service.Id
                        },
                        Customer = new CustomerFillterDto
                        {
                            Id = s.CustomerId,
                            UserName = s.Customer.UserName,
                            FirstNameEn = s.Customer.FirstNameEn,
                            lastNameEn = s.Customer.lastNameEn,
                        }
                    }
                    )
                    .ToListAsync();

            }
            //data.ForEach(data =>
            //{
            //    data.Service = _unitOfWork.serviceManger.GetAllQurAsync().Where(s => s.Id == data.ServiceId && s.isDeleted == false).Select(s => new Service { Id = s.Id, arabicName = s.arabicName, englishName = s.englishName, description = s.description }).FirstOrDefault();
            //});
            //data.ForEach(data =>
            //{
            //    data.Employee = _userManager.Users.Where(r => r.Id == data.EmployeeId && r.isDeleted == false)
            //    .Select(s => new ApplicationUser { UserName = s.UserName, FirstNameEn = s.FirstNameEn, lastNameEn = s.lastNameEn }).FirstOrDefault();
            //});
            //data.ForEach(data =>
            //{
            //    data.Customer = _userManager.Users.Where(r => r.Id == data.CustomerId && r.isDeleted == false).Select(s => new ApplicationUser { UserName = s.UserName, FirstNameEn = s.FirstNameEn, lastNameEn = s.lastNameEn }).FirstOrDefault();
            //});
            res.Add("Data", data);
            var Result = new ResultDTO<Dictionary<string, dynamic>>()
            {
                Message = "All Booking History",
                State = true,
                Object = res
            };
            return Result;

        }
        public async Task<ResultDTO<Dictionary<string, dynamic>>> AllBookingHistory(BookingHistorySearchFiltrationDTO filtrationDTO)
        {
            var format = "dd/MM/yyyy";
            var formatInfo = new DateTimeFormatInfo()
            {
                ShortDatePattern = format
            };
            var res = new Dictionary<string, dynamic>();
            var data = new List<BookingFilttrationDTO>();
            if (!(string.IsNullOrEmpty(filtrationDTO.ToDate)) && !(string.IsNullOrEmpty(filtrationDTO.FromDate)))
            {
                var de = Convert.ToDateTime(filtrationDTO.ToDate, formatInfo);
                var de2 = Convert.ToDateTime(filtrationDTO.FromDate, formatInfo);
                Expression<Func<Appointment, bool>> Expreisson = s =>
             (s.AppointmentDate >= Convert.ToDateTime(filtrationDTO.FromDate, formatInfo)) && (s.AppointmentDate <= Convert.ToDateTime(filtrationDTO.ToDate, formatInfo));
                data = await _unitOfWork.AppointmentManger.GetAllQurAsync()
                    .Where(Expreisson)
                    .Include(s => s.Customer)
                    .Include(s => s.Service)
                    .Include(s => s.Employee)
                    .Select(s => new BookingFilttrationDTO
                    {
                        Id = s.Id,
                        From = s.From,
                        To = s.To,
                        CustomerId = s.CustomerId,
                        EmployeeId = s.EmployeeId,
                        State = s.State,
                        OtherCustomerPhone = s.OtherCustomerPhone,
                        AppointmentDate = s.AppointmentDate,
                        Creation_Date = s.Creation_Date,
                        Employee = new EmployeeFillterDto
                        {
                            Id = s.EmployeeId,
                            UserName = s.Employee.UserName,
                            FirstNameEn = s.Employee.FirstNameEn,
                            lastNameEn = s.Employee.lastNameEn,
                        },
                        Service = new ServiceFillterDto
                        {
                            arabicName = s.Service.arabicName,
                            englishName = s.Service.englishName,
                            Id = s.Service.Id
                        },
                        Customer = new CustomerFillterDto
                        {
                            Id = s.CustomerId,
                            UserName = s.Customer.UserName,
                            FirstNameEn = s.Customer.FirstNameEn,
                            lastNameEn = s.Customer.lastNameEn,
                        }
                    }
                    )
                    .ToListAsync();
                if (!string.IsNullOrEmpty(filtrationDTO.userId))
                {
                    data = data.Where(s => s.CustomerId == filtrationDTO.userId).ToList();
                }

            }
           
            res.Add("Data", data);
            var Result = new ResultDTO<Dictionary<string, dynamic>>()
            {
                Message = "All Booking History",
                State = true,
                Object = res
            };
            return Result;

        }
        public async Task<ResultDTO<Dictionary<string, dynamic>>> AllBookingHistoryByEmployeeId(string EmployeeId)
        {            
            var res = new Dictionary<string, dynamic>();
            var data = new List<BookingFilttrationDTO>();
            if (EmployeeId != null)
            {
                data = await _unitOfWork.AppointmentManger.GetAllQurAsync().Where(re => re.EmployeeId == EmployeeId && re.isDeleted == false).Include(s => s.Customer)
                    .Include(s => s.Customer)
                    .Include(s => s.Service)
                    .Include(s => s.Employee)
                    .Select(s => new BookingFilttrationDTO
                    {
                        Id = s.Id,
                        From = s.From,
                        To = s.To,
                        CustomerId = s.CustomerId,
                        EmployeeId = s.EmployeeId,
                        State = s.State,
                        OtherCustomerPhone = s.OtherCustomerPhone,
                        AppointmentDate = s.AppointmentDate,
                        Creation_Date = s.Creation_Date,
                        Employee = new EmployeeFillterDto
                        {
                            Id = s.EmployeeId,
                            UserName = s.Employee.UserName,
                            FirstNameEn = s.Employee.FirstNameEn,
                            lastNameEn = s.Employee.lastNameEn,
                        },
                        Service = new ServiceFillterDto
                        {
                            arabicName = s.Service.arabicName,
                            englishName = s.Service.englishName,
                            Id = s.Service.Id
                        },
                        Customer = new CustomerFillterDto
                        {
                            Id = s.CustomerId,
                            UserName = s.Customer.UserName,
                            FirstNameEn = s.Customer.FirstNameEn,
                            lastNameEn = s.Customer.lastNameEn,
                        }
                    }
                    )
                    .ToListAsync();

            }
            else
            {
                data = await _unitOfWork.AppointmentManger.GetAllQurAsync()
                    .Where(re => re.isDeleted == false)
                    .Include(s => s.Customer)
                    .Include(s => s.Service)
                    .Include(s => s.Employee)
                    .Select(s => new BookingFilttrationDTO
                    {
                        Id = s.Id,
                        From = s.From,
                        To = s.To,
                        CustomerId = s.CustomerId,
                        EmployeeId = s.EmployeeId,
                        State = s.State,
                        OtherCustomerPhone = s.OtherCustomerPhone,
                        AppointmentDate = s.AppointmentDate,
                        Creation_Date = s.Creation_Date,
                        Employee = new EmployeeFillterDto
                        {
                            Id = s.EmployeeId,
                            UserName = s.Employee.UserName,
                            FirstNameEn = s.Employee.FirstNameEn,
                            lastNameEn = s.Employee.lastNameEn,
                        },
                        Service = new ServiceFillterDto
                        {
                            arabicName = s.Service.arabicName,
                            englishName = s.Service.englishName,
                            Id = s.Service.Id
                        },
                        Customer = new CustomerFillterDto
                        {
                            Id = s.CustomerId,
                            UserName = s.Customer.UserName,
                            FirstNameEn = s.Customer.FirstNameEn,
                            lastNameEn = s.Customer.lastNameEn,
                        }
                    }
                    )
                    .ToListAsync(); ;

            }
          
            res.Add("Data", data);
            var Result = new ResultDTO<Dictionary<string, dynamic>>()
            {
                Message = "All Booking History",
                State = true,
                Object = res
            };
            return Result;

        }
        public async Task<ResultDTO<Dictionary<string, dynamic>>> AllBookingHistory()
        {
            var res = new Dictionary<string, dynamic>();
            var data = new List<Appointment>();
            data = await _unitOfWork.AppointmentManger.GetAllQurAsync().Where(re => re.isDeleted == false).ToListAsync();
            data.ForEach(data =>
            {
                data.Service = _unitOfWork.serviceManger.GetAllQurAsync().Where(s => s.Id == data.ServiceId && s.isDeleted == false).Select(s => new Service { Id = s.Id, arabicName = s.arabicName, englishName = s.englishName, description = s.description }).FirstOrDefault();
            });
            data.ForEach(data =>
            {
                data.Employee = _userManager.Users.Where(r => r.Id == data.EmployeeId && r.isDeleted == false)
                .Select(s => new ApplicationUser { UserName = s.UserName, FirstNameEn = s.FirstNameEn, lastNameEn = s.lastNameEn }).FirstOrDefault();
            });
            data.ForEach(data =>
            {
                data.Customer = _userManager.Users.Where(r => r.Id == data.CustomerId && r.isDeleted == false).Select(s => new ApplicationUser { UserName = s.UserName, FirstNameEn = s.FirstNameEn, lastNameEn = s.lastNameEn }).FirstOrDefault();
            });
            res.Add("Data", data);
            var Result = new ResultDTO<Dictionary<string, dynamic>>()
            {
                Message = "All Booking History",
                State = true,
                Object = res
            };
            return Result;

        }
        public async Task<ResultDTO<Dictionary<string, dynamic>>> DeleteAppointment(int appointmentId)
        {
            var res = new Dictionary<string, dynamic>();
            res.Add("Object", null);
            var Result = new ResultDTO<Dictionary<string, dynamic>>()
            {
                Message = "All Booking History",
                State = false,
                Object = res
            };
            var data = await _unitOfWork.AppointmentManger.GetAllQurAsync().Where(re => re.Id == appointmentId && re.isDeleted == false).FirstAsync();
            if (data != null)
            {
                data.isDeleted = true;
                var result = await _unitOfWork.AppointmentManger.UpdateAsync(data);
                if (result)
                {
                    res.Add("Object", result);
                    Result = new ResultDTO<Dictionary<string, dynamic>>()
                    {
                        Message = "All Booking History",
                        State = true,
                        Object = res
                    };
                }
                else
                {
                    res.Add("Object", null);
                    Result = new ResultDTO<Dictionary<string, dynamic>>()
                    {
                        Message = "All Booking History",
                        State = false,
                        Object = res
                    };
                }

            }
            else
            {
                res.Add("Object", null);
                Result = new ResultDTO<Dictionary<string, dynamic>>()
                {
                    Message = "All Booking History",
                    State = false,
                    Object = res
                };
            }
            return Result;
        }
        public async Task<ResultDTO<Dictionary<string, dynamic>>> CompleteBooking(int appointmentId)
        {
            var res = new Dictionary<string, dynamic>();
            var Result = new ResultDTO<Dictionary<string, dynamic>>();
            var data = _unitOfWork.AppointmentManger.GetAllQurAsync().Where(re => re.Id == appointmentId && re.isDeleted == false).Include(w => w.Service).Include(e => e.Customer).FirstOrDefault();

            data.State = (int)AllEnumes.StateAppointments.Completed;
            data.Customer.Points = data.Customer.Points + data.Service.Points;
            var ResultUpdated = await _unitOfWork.AppointmentManger.UpdateAsync(data);
            if (ResultUpdated)
            {
                res.Add("Message", "Appointment Completed ");

                Result = new ResultDTO<Dictionary<string, dynamic>>()
                {
                    Message = "Appointment Completed",
                    State = true,
                    Object = res
                };
                return Result;
            }
            res.Add("Errors", "the system note an error");
            Result = new ResultDTO<Dictionary<string, dynamic>>()
            {
                Message = "the system note an error",
                State = false,
                Object = res
            };

            return Result;
        }
        public async Task<ResultDTO<Dictionary<string, dynamic>>> GetAppointmentsByMobile(string Mobile)
        {
            var res = new Dictionary<string, dynamic>();
            var list = await _unitOfWork.AppointmentManger.GetAllQurAsync().Where(r => r.isDeleted == false).OrderByDescending(d => d.AppointmentDate)
            .Where(r => r.OtherCustomerPhone == Mobile && r.AppointmentDate >= DateTime.Now.Date && r.State == 3).Select(e => new BookingRequestDTO
            {
                EmployeeId = e.EmployeeId,
                CustomerId = e.CustomerId,
                ServiceId = e.ServiceId,
                From = Convert.ToString(e.From),
                To = Convert.ToString(e.To),
                ServiceName = e.Service.englishName,
                AppointmentDate = e.AppointmentDate,
                AppointmentId = e.Id,
                State = e.State.ToString(),
                AnotherCustomerPhone = e.OtherCustomerPhone == null ? "" : e.OtherCustomerPhone,
                CreatedBy = e.CreatedBy
            }).ToListAsync();
            list.ForEach(r => r.State = Enum.GetName(typeof(AllEnumes.StateAppointments), int.Parse(r.State)));
            list.ForEach(data =>
            {
                data.EmployeeName = _userManager.Users.Where(r => r.Id == data.EmployeeId && r.isDeleted == false).FirstOrDefault().UserName;
            });
            var data = new List<dynamic>();
            list.ForEach(r =>
            {
                var listAppointment = list.Where(e => e.AppointmentDate == r.AppointmentDate).ToList();
                var listdata = data.Where(x => x.Date == r.AppointmentDate).ToList().Count;


                if (listdata == 0)
                {
                    data.Add(new listValue { Date = r.AppointmentDate, List = listAppointment });

                }
            });
            res.Add("Appointment", data);

            var Result = new ResultDTO<Dictionary<string, dynamic>>()
            {
                Message = "Appointments List Of User",
                State = true,
                Object = res
            };
            return Result;
        }

    }
    public class listValue
    {
        public DateTime Date { get; set; }
        public dynamic List { get; set; }
    }
}
