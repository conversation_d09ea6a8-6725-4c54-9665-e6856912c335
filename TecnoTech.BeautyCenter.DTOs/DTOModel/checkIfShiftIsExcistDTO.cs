using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TecnoTech.BeautyCenter.DTOs.DTOModel
{
    public class checkIfShiftIsExcistDTO
    {
        public int shiftId { get; set; }
        public string EmployeeId { get; set; }
        public DateTime ShiftDate { get; set; }

    }
    public class ResultfShiftStateDTO
    {
        public bool Editable { get; set; }
        public bool deletable{ get; set; }
        public bool Swable{ get; set; }
    }
}
