using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TecnoTech.BeautyCenter.DTOs.DTOModel
{
    public class BookingFilttrationDTO
    {
        public int Id { set; get; }
        public TimeSpan From { set; get; }
        public TimeSpan To { set; get; }
        public string CustomerId { set; get; }
        public string EmployeeId { set; get; }
        public EmployeeFillterDto Employee { set; get; }
        public ServiceFillterDto Service { set; get; }
        public CustomerFillterDto Customer { set; get; }
        public int State { set; get; }
        public string OtherCustomerPhone { set; get; }
        public DateTime AppointmentDate { get; set; }
        public DateTime Creation_Date { get; set; }

    }
    public class EmployeeFillterDto
    {
        public string FirstNameEn { set; get; }
        public string FirstNameAr { set; get; }
        public string lastNameEn { set; get; }
        public string lastNameAr { set; get; }
        public string UserName { set; get; }
        public string Id { set; get; }
    }
    public class CustomerFillterDto
    {
        public string FirstNameEn { set; get; }
        public string FirstNameAr { set; get; }
        public string lastNameEn { set; get; }
        public string lastNameAr { set; get; }
        public string UserName { set; get; }
        public string Id { set; get; }
    }
    public class ServiceFillterDto
    {
        public string englishName { get; set; }
        public string arabicName { get; set; }
        public int Id { get; set; }

    }
}
