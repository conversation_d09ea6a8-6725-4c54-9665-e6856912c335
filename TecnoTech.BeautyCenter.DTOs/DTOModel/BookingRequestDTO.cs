using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TecnoTech.BeautyCenter.DTOs.DTOModel
{
    public class BookingRequestDTO
    {
        public string From { get; set; }
        public string To { get; set; }
        public DateTime AppointmentDate { get; set; }
        [Required]
        public int ServiceId { get; set; }
        public string ServiceName { get; set; }
        public int AppointmentId { get; set; }
        public string CustomerId { get; set; }
        public string EmployeeId { get; set; }
        public string EmployeeName { get; set; }
        public string CreatedBy { get; set; }
        public string State { get; set; }
        public string AnotherCustomerPhone { get; set; }
        public double Price { get; set; }

    }
}
