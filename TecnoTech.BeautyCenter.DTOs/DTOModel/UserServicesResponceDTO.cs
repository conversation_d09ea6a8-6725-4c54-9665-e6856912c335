using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TecnoTech.BeautyCenter.DTOs.DTOModel
{
    public class UserServicesResponceDTO
    {
        public string description { get; set; }
        public string arabicName { get; set; }
        public string englishName { get; set; }
        public int? averageServiceTimeMin { get; set; }
        public double? Price { get; set; }
        public int ServicesId { get; set; }
        public DateTime? LastEiteDate { get; set; }
        public string LastEditeBy { get; set; }
        public DateTime Creation_Date { get; set; }
        public string CreatedBy { get; set; }
        public bool? isDeleted { get; set; }
        public DateTime? DeletedDate { get; set; }
        public string DeletedBy { get; set; }
        public bool? Isapproved { get; set; }
        public bool? IsSelected { get; set; }
    }
}
