using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TecnoTech.BeautyCenter.DTOs.DTOModel
{
    public class NotificationsDTO
    {
        public int Id { get; set; }
        public string From { get; set; }
        public string To { get; set; }
        public DateTime AppointmentDate { get; set; }
        public int ServiceId { get; set; }
        public string ServiceName { get; set; }
        public int AppointmentId { get; set; }
        public string CustomerId { get; set; }
        public string CustomerName { get; set; }
        public string EmployeeId { get; set; }
        public string EmployeeName { get; set; }
        public string State { get; set; }
        public string CustomerPhone { get; set; }
        public DateTime? LastEiteDate { get; set; }
        public DateTime Creation_Date { get; set; }
        public bool IsDeleted { get; set; }
    }
    public class NotificationsEditDTO
    {
        public string From { get; set; }
        public string To { get; set; }
        public DateTime AppointmentDate { get; set; }
        public int ServiceId { get; set; }
        public string ServiceName { get; set; }
        public int AppointmentId { get; set; }
        public string CustomerId { get; set; }
        public string CustomerName { get; set; }
        public string EmployeeId { get; set; }
        public string EmployeeName { get; set; }
        public string State { get; set; }
        public string CustomerPhone { get; set; }
        public bool IsDeleted { get; set; }
        public int Id { get; set; }


    }
}
