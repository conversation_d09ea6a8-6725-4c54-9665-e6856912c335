using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TecnoTech.BeautyCenter.DTOs.DTOModel
{
    public class PayBillDTO
    {
        public PayBillDTO()
        {
            AppointmentDTO = new List<PayAppointmentDTO>();
        }
        public string CreateBy { get; set; }
        public string PhoneNumber { get; set; }
        public string CustomerId { get; set; }
        public List<PayAppointmentDTO> AppointmentDTO { get; set; }
    }
    public class PayAppointmentDTO
    {
        public int AppointmentId { get; set; }
        public double AppointmentPrice { get; set; }
    }
}
