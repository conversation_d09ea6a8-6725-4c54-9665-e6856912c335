using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TecnoTech.BeautyCenter.DTOs.DTOModel
{
    public class SlotsShiftDTO
    {
        public string Id { get; set; }
        public string UserName { get; set; }
        public string DayDate { get; set; }
        public string From { get; set; }
        public string to { get; set; }
        public int PhoneNumber { get; set; }
        //public Service: null

        public List<slotsDTO> slots { get; set; }

    }
    public class slotsDTO
    {
        public string Id { get; set; }
        public string Start { set; get; }
        public string End { set; get; }
        public string SlotsDate { set; get; }
        public int Status { set; get; }
        public int ShiftId { set; get; }
        public int AppointmentId { set; get; }
        public int Appointment { set; get; }
    }
    public class GetEmployeeShiftByEmpIdDTO
    {
        public int ShiftId { get; set; }
        public TimeSpan to { get; set; }
        public TimeSpan From { get; set; }
        public DateTime DayDate { get; set; }
        public int? State { get; set; }
        public List<SmallSlotsDTO> slots { get; set; }
    }
    public class SmallSlotsDTO
    {
        public int? Status { get; set; }
        public TimeSpan? Start { get; set; }
    }
}
