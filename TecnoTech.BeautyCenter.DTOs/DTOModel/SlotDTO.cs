using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TecnoTech.BeautyCenter.DTOs.DTOModel
{
   public class SlotsDTO
    {
        public int Id { get; set; }
        public TimeSpan? start { get; set; }
        public TimeSpan? end { get; set; }
        public int ShiftId{ get; set; }
        public int Status { get; set; }
        public int? serviceId { get; set; }
        public DateTime slotsDate { get; set; }
    }

    public class EmployeeWithShiftsSlotsDTO
    {
        public EmployeeWithShiftsSlotsDTO()
        {
            slotsList = new List<SlotsDTO>();
        }
        public List<SlotsDTO> slotsList { get; set; }
        public string EmployeeId { get; set; }
    }
    public class AvailableAppointMent
    {
        public AvailableAppointMent()
        {
            AppointmentslotsList = new List<SlotsDTO>();
        }
        public string EmployeeId { get; set; }
        public TimeSpan? from { get; set; }
        public TimeSpan? to { get; set; }
        public string AppointmentDate { get; set; }
        public List<SlotsDTO> AppointmentslotsList { get; set; }


    }
}
