using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TecnoTech.BeautyCenter.DTOs.DTOModel
{
    public class RequestShiftDTO
    {
        [Required]
        public string from { get; set; }
        public string to { get; set; }
        public DateTime dateTimeNow { get; set; }
        public string EmployeeId { get; set; }
        //public string CustomerId { get; set; }
        //public string Status { get; set; }
        public string CreatedBy{ get; set; }
        public int? ShiftType { get; set; }

    }
}
