using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TecnoTech.BeautyCenter.DTOs.DTOModel
{
    public class ServicesDTO
    {
        public string description { get; set; }
        public string arabicName { get; set; }
        public string englishName { get; set; }
        public int? averageServiceTimeMin { get; set; }
        public double? Price { get; set; }
        public int Id { get; set; }
        public DateTime? LastEiteDate { get; set; }
        public string LastEditeBy { get; set; }
        public DateTime Creation_Date { get; set; }
        public string CreatedBy { get; set; }
        public bool? isDeleted { get; set; }
        public DateTime? DeletedDate { get; set; }
        public string DeletedBy { get; set; }
        public bool? Isapproved { get; set; }
        public int SlotsNumber { get; set; }
        public string Icon { get; set; }
        public int Points { get; set; }


    }
    public class ServiceEditDTO
    {
        public string description { get; set; }
        public string arabicName { get; set; }
        public string englishName { get; set; }
        public int? averageServiceTimeMin { get; set; }
        public double? Price { get; set; }
        public int Id { get; set; }
        public int SlotsNumber { get; set; }
        public string backgroundphoto { get; set; }

    }

    public class SignEmployeeToServicesDTO
    {
        public SignEmployeeToServicesDTO()
        {
            ServicesListId = new List<int>();
        }
        [Required]
        public string EemployeeId { get; set; }
        public string CreatedBy { get; set; }
        [Required]
        public List<int> ServicesListId { get; set; }
    }  
}
