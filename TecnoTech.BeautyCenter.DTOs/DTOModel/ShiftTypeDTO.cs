using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TecnoTech.BeautyCenter.DTOs.DTOModel
{
    public class AddShiftTypeDTO
    {
        [Required]
        public int Hours { get; set; }
        [Required]
        public string ShiftTypeName { get; set; }
    }
    public class UpdateShiftTypeDTO
    {
        [Required]
        public int ShiftTypeId { get; set; }
        [Required]
        public int Hours { get; set; }
        [Required]
        public string ShiftTypeName { get; set; }
    }
}
