using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TecnoTech.BeautyCenter.DTOs.DTOModel
{
    public class UploadFileDTO
    {
        public UploadFileDTO()
        {
            files = new List<IFormFile>();
        }
        public string ReferenceKey { get; set; }
        public string ReferencePath { get; set; }
        public string ReferenceId { get; set; }
        public string CreatedBy { get; set; }
        public ICollection<IFormFile> files { get; set; }
        public dynamic uploadDirecotroy { get; set; }

    }
}
