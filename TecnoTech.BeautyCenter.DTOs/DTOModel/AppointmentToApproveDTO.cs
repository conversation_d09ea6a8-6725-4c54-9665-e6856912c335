using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TecnoTech.BeautyCenter.DTOs.DTOModel
{
    public class AppointmentToApproveDTO
    {
        public int Id { get; set; }
        public TimeSpan From { get; set; }
        public TimeSpan To { get; set; }
        public DateTime AppointmentDate { get; set; }
        public DateTime Creation_Date { get; set; }

        public string ServiceArabicName { get; set; }
        public string ServiceEnglishName { get; set; }
        public int ServiceId { get; set; }
        public string CustomerName { get; set; }
        public string CustomerPhoneNumber { get; set; }
        public string CustomerEmail { get; set; }
        public string CustomerId { get; set; }
        public string EmployeeName { get; set; }
        public string EmployeeId { get; set; }
        public string EmployeePhoneNumber { get; set; }
        public string EmployeeEmail { get; set; }
        public int State { get; set; }
        public bool? isDeleted { get; set; }
    }
}
