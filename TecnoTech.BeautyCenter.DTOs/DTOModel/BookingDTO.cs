using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TecnoTech.BeautyCenter.DTOs.DTOModel
{
    public class BookingDTO
    {
        public BookingDTO()
        {
            BookingModelSlotsList = new List<int>();
        }
        [Required]
        public int ServiceId { get; set; }
        public string OtherCustomerPhone { get; set; }
        public string CustomerId { get; set; }
        [Required]
        public string EmployeeId { get; set; }
        [Required]
        public string CreateById { get; set; }
        [Required]
        public DateTime? from { get; set; }
        [Required]
        public DateTime? to { get; set; }
        [Required]
        public string AppointmentDate { get; set; }
        [Required]
        //public List<BookingSlotsDTO> BookingModelSlotsList { get; set; }
        public List<int> BookingModelSlotsList { get; set; }
    }
}
