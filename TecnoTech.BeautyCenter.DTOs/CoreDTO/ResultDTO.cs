using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TecnoTech.BeautyCenter.DTOs.CoreDTO
{
    public class ResultDTO<TEntity>
    {
        public TEntity Object { get; set; }
        public string Message{ get; set; }
        public bool State{ get; set; }
    } 
    public class ResultFileDTO
    {

        public byte[] fileContents { get; set; }
        public string contentType { get; set; }       
        public string Message{ get; set; }
        public bool State{ get; set; }
    } 
    //public class ResultDTO<TEntity>
    //{

    //    public TEntity Object { get; set; }
    //    public string Message{ get; set; }
    //    public bool State{ get; set; }
    //}
}
