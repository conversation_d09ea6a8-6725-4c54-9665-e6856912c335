using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TecnoTech.BeautyCenter.DAL.SqlServer.Context;
using TecnoTech.BeautyCenter.DAL.SqlServer.Models;
using TecnoTech.BeautyCenter.UnitOfWork.Manger;

namespace TecnoTech.BeautyCenter.UnitOfWork.unitOfWork
{
    public class unitOfWork: IUnitOfWork

    {
        private readonly BeautyCenterContext Context;

        public unitOfWork(BeautyCenterContext context)
        {
            Context = context;
        }
        public ApplicationUserManger applicationUserManger
        {
            get
            {
                return new ApplicationUserManger(Context);
            }
        }      
        public AppointmentManger AppointmentManger
        {
            get
            {
                return new AppointmentManger(Context);
            }
        } 
        public ServiceManger serviceManger
        {
            get
            {
                return new ServiceManger(Context);
            }
        } 
        public SlotManger SlotManger
        {
            get
            {
                return new SlotManger(Context);
            }
        }       
        public EmpServManger EmpServManger
        {
            get
            {
                return new EmpServManger(Context);
            }
        }
        public ShiftManger ShiftManger
        {
            get
            {
                return new ShiftManger(Context);
            }
        }
        public AppoimentManger bookingAppoimentsManger
        {
            get
            {
                return new AppoimentManger(Context);
            }
        }
        public GeneralPhotosManger GeneralPhotosManger
        {
            get
            {
                return new GeneralPhotosManger(Context);
            }
        } 
        public BillManger BillManger
        {
            get
            {
                return new BillManger(Context);
            }
        }
        public BillItemsManger BillItemsManger
        {
            get
            {
                return new BillItemsManger(Context);
            }
        }
        public SettingsManger SettingsManger
        {
            get
            {
                return new SettingsManger(Context);
            }
        }    
        public ShiftTypeManger ShiftTypeManger
        {
            get
            {
                return new ShiftTypeManger(Context);
            }
        }

        public NotificationManger NotificationManger
        {
            get
            {
                return new NotificationManger(Context);
            }
        }
    }
}
