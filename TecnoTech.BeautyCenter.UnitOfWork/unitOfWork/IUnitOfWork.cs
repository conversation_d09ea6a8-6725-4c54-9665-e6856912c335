using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TecnoTech.BeautyCenter.UnitOfWork.Manger;

namespace TecnoTech.BeautyCenter.UnitOfWork.unitOfWork
{
    public interface IUnitOfWork
    {
        public ApplicationUserManger applicationUserManger { get; }
        public AppointmentManger AppointmentManger { get; }
        public ServiceManger serviceManger { get; }
        public SlotManger SlotManger { get; }
        public EmpServManger EmpServManger { get; }
        public ShiftManger ShiftManger { get; }
        public AppoimentManger bookingAppoimentsManger { get; }
        public GeneralPhotosManger GeneralPhotosManger { get; }
        public BillManger BillManger { get; }
        public BillItemsManger BillItemsManger { get; } 
        public SettingsManger SettingsManger { get; }
        public ShiftTypeManger ShiftTypeManger { get; }
        public NotificationManger NotificationManger { get; }
    }
}
