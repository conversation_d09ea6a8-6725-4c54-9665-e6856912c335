using Microsoft.EntityFrameworkCore.Migrations;

namespace TecnoTech.BeautyCenter.DAL.SqlServer.Migrations
{
    public partial class slotsUpdate : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Slot_AspNetUsers_CustomerId",
                table: "Slot");

            migrationBuilder.DropForeignKey(
                name: "FK_Slot_AspNetUsers_EmployeeId",
                table: "Slot");

            migrationBuilder.RenameColumn(
                name: "EmployeeId",
                table: "Slot",
                newName: "ApplicationUserId1");

            migrationBuilder.RenameColumn(
                name: "CustomerId",
                table: "Slot",
                newName: "ApplicationUserId");

            migrationBuilder.RenameIndex(
                name: "IX_Slot_EmployeeId",
                table: "Slot",
                newName: "IX_Slot_ApplicationUserId1");

            migrationBuilder.RenameIndex(
                name: "IX_Slot_CustomerId",
                table: "Slot",
                newName: "IX_Slot_ApplicationUserId");

            migrationBuilder.AddForeignKey(
                name: "FK_Slot_AspNetUsers_ApplicationUserId",
                table: "Slot",
                column: "ApplicationUserId",
                principalTable: "AspNetUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Slot_AspNetUsers_ApplicationUserId1",
                table: "Slot",
                column: "ApplicationUserId1",
                principalTable: "AspNetUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Slot_AspNetUsers_ApplicationUserId",
                table: "Slot");

            migrationBuilder.DropForeignKey(
                name: "FK_Slot_AspNetUsers_ApplicationUserId1",
                table: "Slot");

            migrationBuilder.RenameColumn(
                name: "ApplicationUserId1",
                table: "Slot",
                newName: "EmployeeId");

            migrationBuilder.RenameColumn(
                name: "ApplicationUserId",
                table: "Slot",
                newName: "CustomerId");

            migrationBuilder.RenameIndex(
                name: "IX_Slot_ApplicationUserId1",
                table: "Slot",
                newName: "IX_Slot_EmployeeId");

            migrationBuilder.RenameIndex(
                name: "IX_Slot_ApplicationUserId",
                table: "Slot",
                newName: "IX_Slot_CustomerId");

            migrationBuilder.AddForeignKey(
                name: "FK_Slot_AspNetUsers_CustomerId",
                table: "Slot",
                column: "CustomerId",
                principalTable: "AspNetUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Slot_AspNetUsers_EmployeeId",
                table: "Slot",
                column: "EmployeeId",
                principalTable: "AspNetUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }
    }
}
