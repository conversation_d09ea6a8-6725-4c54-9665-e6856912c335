using Microsoft.EntityFrameworkCore.Migrations;

namespace TecnoTech.BeautyCenter.DAL.SqlServer.Migrations
{
    public partial class slotsUser1 : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "EmployeeId",
                table: "AppointmentSlot",
                type: "nvarchar(450)",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_AppointmentSlot_EmployeeId",
                table: "AppointmentSlot",
                column: "EmployeeId");

            migrationBuilder.AddForeignKey(
                name: "FK_AppointmentSlot_AspNetUsers_EmployeeId",
                table: "AppointmentSlot",
                column: "EmployeeId",
                principalTable: "AspNetUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_AppointmentSlot_AspNetUsers_EmployeeId",
                table: "AppointmentSlot");

            migrationBuilder.DropIndex(
                name: "IX_AppointmentSlot_EmployeeId",
                table: "AppointmentSlot");

            migrationBuilder.DropColumn(
                name: "EmployeeId",
                table: "AppointmentSlot");
        }
    }
}
