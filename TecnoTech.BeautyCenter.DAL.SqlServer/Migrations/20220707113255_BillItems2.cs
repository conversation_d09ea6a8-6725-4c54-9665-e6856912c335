using Microsoft.EntityFrameworkCore.Migrations;

namespace TecnoTech.BeautyCenter.DAL.SqlServer.Migrations
{
    public partial class BillItems2 : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Bill_Appointment_AppointmentId",
                table: "Bill");

            migrationBuilder.DropIndex(
                name: "IX_Bill_AppointmentId",
                table: "Bill");

            migrationBuilder.DropColumn(
                name: "AppointmentId",
                table: "Bill");

            migrationBuilder.AddColumn<int>(
                name: "BillId",
                table: "BillItems",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.CreateIndex(
                name: "IX_BillItems_BillId",
                table: "BillItems",
                column: "BillId");

            migrationBuilder.AddForeignKey(
                name: "FK_BillItems_Bill_BillId",
                table: "BillItems",
                column: "BillId",
                principalTable: "Bill",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_BillItems_Bill_BillId",
                table: "BillItems");

            migrationBuilder.DropIndex(
                name: "IX_BillItems_BillId",
                table: "BillItems");

            migrationBuilder.DropColumn(
                name: "BillId",
                table: "BillItems");

            migrationBuilder.AddColumn<int>(
                name: "AppointmentId",
                table: "Bill",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.CreateIndex(
                name: "IX_Bill_AppointmentId",
                table: "Bill",
                column: "AppointmentId");

            migrationBuilder.AddForeignKey(
                name: "FK_Bill_Appointment_AppointmentId",
                table: "Bill",
                column: "AppointmentId",
                principalTable: "Appointment",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
