using Microsoft.EntityFrameworkCore.Migrations;

namespace TecnoTech.BeautyCenter.DAL.SqlServer.Migrations
{
    public partial class slotsUpdate2 : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Slot_AspNetUsers_ApplicationUserId",
                table: "Slot");

            migrationBuilder.DropForeignKey(
                name: "FK_Slot_AspNetUsers_ApplicationUserId1",
                table: "Slot");

            migrationBuilder.DropIndex(
                name: "IX_Slot_ApplicationUserId",
                table: "Slot");

            migrationBuilder.DropIndex(
                name: "IX_Slot_ApplicationUserId1",
                table: "Slot");

            migrationBuilder.DropColumn(
                name: "ApplicationUserId",
                table: "Slot");

            migrationBuilder.DropColumn(
                name: "ApplicationUserId1",
                table: "Slot");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "ApplicationUserId",
                table: "Slot",
                type: "nvarchar(450)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ApplicationUserId1",
                table: "Slot",
                type: "nvarchar(450)",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_Slot_ApplicationUserId",
                table: "Slot",
                column: "ApplicationUserId");

            migrationBuilder.CreateIndex(
                name: "IX_Slot_ApplicationUserId1",
                table: "Slot",
                column: "ApplicationUserId1");

            migrationBuilder.AddForeignKey(
                name: "FK_Slot_AspNetUsers_ApplicationUserId",
                table: "Slot",
                column: "ApplicationUserId",
                principalTable: "AspNetUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Slot_AspNetUsers_ApplicationUserId1",
                table: "Slot",
                column: "ApplicationUserId1",
                principalTable: "AspNetUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }
    }
}
