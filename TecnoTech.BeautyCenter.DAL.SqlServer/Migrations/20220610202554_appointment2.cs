using Microsoft.EntityFrameworkCore.Migrations;

namespace TecnoTech.BeautyCenter.DAL.SqlServer.Migrations
{
    public partial class appointment2 : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Slot_Services_ServiceId",
                table: "Slot");

            migrationBuilder.DropIndex(
                name: "IX_Slot_ServiceId",
                table: "Slot");

            migrationBuilder.DropColumn(
                name: "ServiceId",
                table: "Slot");

            migrationBuilder.AddColumn<string>(
                name: "CustomerPhoneNumber2",
                table: "Appointment",
                type: "nvarchar(max)",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CustomerPhoneNumber2",
                table: "Appointment");

            migrationBuilder.AddColumn<int>(
                name: "ServiceId",
                table: "Slot",
                type: "int",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_Slot_ServiceId",
                table: "Slot",
                column: "ServiceId");

            migrationBuilder.AddForeignKey(
                name: "FK_Slot_Services_ServiceId",
                table: "Slot",
                column: "ServiceId",
                principalTable: "Services",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }
    }
}
