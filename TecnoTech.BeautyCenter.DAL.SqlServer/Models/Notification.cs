using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TecnoTech.BeautyCenter.DAL.SqlServer.Abstract;

namespace TecnoTech.BeautyCenter.DAL.SqlServer.Models
{
    public class Notification 
    {
        public int Id { get; set; }
        public string From { get; set; }
        public string To { get; set; }
        [Column(TypeName = "date")]
        public DateTime AppointmentDate { get; set; }
        public int? AppointmentId { get; set; }
        public int? ServiceId { get; set; }
        public string ServiceName { get; set; }

        public string? CustomerId { get; set; }
        public string? CustomerName { get; set; }

        public string? EmployeeId { get; set; }
        public string? EmployeeName { get; set; }

        public DateTime? LastEiteDate { get; set; }
        public DateTime Creation_Date { get; set; }
        public bool? isDeleted { get; set; } = false;
        public string CustomerPhone { get; set; } 

    }
}