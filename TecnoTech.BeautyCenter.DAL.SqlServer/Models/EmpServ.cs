using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TecnoTech.BeautyCenter.DAL.SqlServer.Abstract;

namespace TecnoTech.BeautyCenter.DAL.SqlServer.Models
{
    public class EmpServ: BasicsInfo
    {
        [ForeignKey("Employee")]
        public string EmployeeId { get; set; }
        [ForeignKey("Service")]
        public int ServiceId { get; set; }
        public virtual ApplicationUser Employee { get; set; }
        public virtual Service Service{ get; set; }
    }
}
