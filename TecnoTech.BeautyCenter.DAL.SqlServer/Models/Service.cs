using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TecnoTech.BeautyCenter.DAL.SqlServer.Abstract;

namespace TecnoTech.BeautyCenter.DAL.SqlServer.Models
{
    public class Service : BasicsInfo
    {
        public Service()
        {
            Employee = new HashSet<ApplicationUser>();
            EmpServ = new HashSet<EmpServ>();          
        }
        public string description { get; set; }
        public string arabicName { get; set; }
        public string englishName { get; set; }
        public int averageServiceTimeMin { get; set; }
        public double Price { get; set; }
        public int SlotsNumber { get; set; }
        public string Icon { get; set; }
        public int Points { get; set; }
        public virtual ICollection< ApplicationUser> Employee { get; set; }       
        public virtual ICollection<EmpServ> EmpServ { get; set; }       
    }
}
