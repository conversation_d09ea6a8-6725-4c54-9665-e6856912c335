using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TecnoTech.BeautyCenter.DAL.SqlServer.Abstract;

namespace TecnoTech.BeautyCenter.DAL.SqlServer.Models
{
    public class BookingRequest : BasicsInfo
    {
        [Column(TypeName = "time")]
        public TimeSpan From { get; set; }

        [Column(TypeName = "time")]
        public TimeSpan To { get; set; }

        [Column(TypeName = "date")]
        public DateTime AppointmentDate { get; set; }

        [ForeignKey("Service")]
        public int? ServiceId { get; set; }

        [ForeignKey("Customer")]
        public string? CustomerId { get; set; }

        [ForeignKey("Employee")]
        public string? EmployeeId { get; set; }
        public int State { get; set; }    
    }
}
