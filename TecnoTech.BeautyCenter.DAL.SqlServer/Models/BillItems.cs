using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TecnoTech.BeautyCenter.DAL.SqlServer.Abstract;

namespace TecnoTech.BeautyCenter.DAL.SqlServer.Models
{
    public class BillItems: BasicsInfo
    {
        public double ItemPrice { get; set; }
        [ForeignKey(nameof(appointment))]
        public int appointmentId { get; set; }
        public Appointment appointment { get; set; }

        [Foreign<PERSON><PERSON>(nameof(Bill))]
        public int BillId { get; set; }
        public Bill Bill { get; set; }
    }
}
