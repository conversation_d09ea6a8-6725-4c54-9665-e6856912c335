using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TecnoTech.BeautyCenter.DAL.SqlServer.Abstract;

namespace TecnoTech.BeautyCenter.DAL.SqlServer.Models
{
    public class Appointment:BasicsInfo
    {
        public Appointment()
        {
            Slots = new HashSet<Slot>();            
        }
        [Column(TypeName = "time")]
        public TimeSpan From { get; set; }
        [Column(TypeName = "time")]
        public TimeSpan To { get; set; }
        [Column(TypeName = "date")]
        public DateTime AppointmentDate { get; set; }
        [ForeignKey("Service")]
        public int ServiceId { get; set; }
        [ForeignKey("Customer")]
        public string? CustomerId { get; set; }       
        [ForeignKey("Employee")]
        public string? EmployeeId { get; set; }
        public virtual ApplicationUser Employee { get; set; }
        public virtual ICollection<Slot> Slots{ get; set; }
        public int State { get; set; }
        //public double Price { get; set; }
        public virtual Service Service { get; set; }
        public virtual ApplicationUser Customer { get; set; }
        public string OtherCustomerPhone { get; set; }
        //public string alternativePhoneNumber { get; set; }
        public BillItems billItems { get; set; }

    }
}
