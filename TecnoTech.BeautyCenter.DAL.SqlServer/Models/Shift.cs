using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TecnoTech.BeautyCenter.DAL.SqlServer.Abstract;

namespace TecnoTech.BeautyCenter.DAL.SqlServer.Models
{
    public class Shift: BasicsInfo
    {
        public Shift()
        {
            slots = new HashSet<Slot>();
        }
        [Column(TypeName = "time")]
        public TimeSpan From { get; set; }
        [Column(TypeName = "time")]
        public TimeSpan to { get; set; }
        [Column(TypeName = "date")]
        public DateTime DayDate { get; set; }
        [ForeignKey("Employee")]
        public string EmployeeId { get; set; }
        public int? State { get; set; }
        public virtual ApplicationUser Employee { get; set; }
        public virtual ICollection<Slot> slots { get; set; }
    }
}
