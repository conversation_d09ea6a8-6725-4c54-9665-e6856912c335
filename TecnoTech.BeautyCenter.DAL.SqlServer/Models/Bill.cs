using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TecnoTech.BeautyCenter.DAL.SqlServer.Abstract;

namespace TecnoTech.BeautyCenter.DAL.SqlServer.Models
{
    public class Bill : BasicsInfo
    {
        public Bill()
        {
            billItems = new HashSet<BillItems>();
        }
        [ForeignKey(nameof(Customer))]
        public string CustomerId { get; set; }
        public ApplicationUser Customer{ get; set; }
        public double Amount { get; set; }        
        public virtual ICollection<BillItems> billItems { get; set; }      
        
    }
}
