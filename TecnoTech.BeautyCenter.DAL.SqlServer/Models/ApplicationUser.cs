using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TecnoTech.BeautyCenter.DAL.SqlServer.Models
{
    [Index(nameof(PhoneNumber), IsUnique = true)]
    public class ApplicationUser : IdentityUser
    {
        public ApplicationUser()
        {
            shifts = new HashSet<Shift>();
            Bills = new HashSet<Bill>();
            EmployeesAppointment = new HashSet<Appointment>();
            CustomersAppointment = new HashSet<Appointment>();
            EmpServ = new List<EmpServ>();
        }

        [Column(TypeName = "date")]
        public DateTime? LastEiteDate { get; set; }
        [StringLength(100)]
        public string LastEditeBy { get; set; }
        [Column(TypeName = "date")]
        public DateTime Creation_Date { get; set; }
        [StringLength(100)]
        public string CreatedBy { get; set; }
        public bool? isDeleted { get; set; }
        public bool? state { get; set; }
        public bool? Isapproved { get; set; }
        [StringLength(100)]
        public string UsernameEn { get; set; }
        [StringLength(100)]
        public string FirstNameEn { get; set; }
        [StringLength(100)]
        public string lastNameEn{ get; set; }
        [StringLength(100)]
        public string UsernameAr { get; set; }
        [StringLength(100)]
        public string FirstNameAr { get; set; }
        [StringLength(100)]
        public string lastNameAr { get; set; }
        [Required]
        // User Type 
        public int UserType { get; set; }
        [EmailAddress]
        public string ExternalEmail { get; set; }
        [Column(TypeName = "date")]
        public DateTime Birthdate { get; set; }
        public int Points { get; set; }
        [ForeignKey(nameof(Service))]
        public int? ServiceId { get; set; }
        public string Image { get; set; }
        public string device_token { get; set; }
        public string fingerPrintIdAndroid { get; set; }
        public virtual Service Service { get; set; }
        public virtual ICollection<Shift>shifts { get; set; }
        public virtual ICollection<Bill> Bills { get; set; }
        public virtual ICollection<Appointment> EmployeesAppointment { get; set; }
        public virtual ICollection<Appointment> CustomersAppointment { get; set; }
        public virtual ICollection<EmpServ> EmpServ { get; set; }
    }
}
