using System;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json.Serialization;

namespace TecnoTech.BeautyCenter.DAL.SqlServer.Models
{
    public class Slot
    {
        public int Id { get; set; }
        //[Column(TypeName = "date")]
        //public DateTime Start { get; set; }
        [Column(TypeName = "time")]
        public TimeSpan? Start { get; set; }

        [Column(TypeName = "time")]
        public TimeSpan End { get; set; }
        [Column(TypeName = "date")]
        public DateTime SlotsDate { get; set; }
        public int Status { get; set; }  
        
        //[ForeignKey("Service")]
        //public int? ServiceId { get; set; }
        //public Service Service { get; set; }

        [ForeignKey("Shift")]
        public int ShiftId { get; set; }
        public virtual Shift Shift { get; set; }
        //[ForeignKey("Customer")]
        //public string? CustomerId { get; set; }

        //public ApplicationUser Customer { get; set; }
        //[ForeignKey("Employee")]
        //public string? EmployeeId { get; set; }        
        //public ApplicationUser Employee { get; set; }

        [ForeignKey("Appointment")]
        public int? AppointmentId { get; set; }
        public virtual Appointment Appointment { get; set; }

    }
}
