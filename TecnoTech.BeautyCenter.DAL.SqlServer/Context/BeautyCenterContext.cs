using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TecnoTech.BeautyCenter.DAL.SqlServer.Models;

namespace TecnoTech.BeautyCenter.DAL.SqlServer.Context
{
    public class BeautyCenterContext : IdentityDbContext
    {
        public BeautyCenterContext(DbContextOptions<BeautyCenterContext> options)
          : base(options)
        {
        }
        //public virtual DbSet<Post> Posts { get; set; }
        public virtual DbSet<ApplicationUser> applicationUsers { get; set; }
        public virtual DbSet<Service> Services { get; set; }    
        public virtual DbSet<Slot> Slot { get; set; }
        public virtual DbSet<Shift> Shift { get; set; }
        public virtual DbSet<EmpServ> EmpServ { get; set; }
        public virtual DbSet<Appointment> Appointment { get; set; }
        public virtual DbSet<GeneralPhoto> GeneralPhotos { get; set; }
        public virtual DbSet<BillItems> BillItems { get; set; }
        public virtual DbSet<Settings> Settings { get; set; }
        public virtual DbSet<ShiftType> ShiftTypes { get; set; }
        public virtual DbSet<Notification> Notifications { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

       //     modelBuilder.Entity<Slot>()
       // .HasOne(p => p.Customer)
       // .WithMany(t => t.CustomersSlots)
       // .HasForeignKey(m => m.CustomerId)
       // .OnDelete(DeleteBehavior.Restrict);


       //     modelBuilder.Entity<Slot>()
       //.HasOne(p => p.Employee)
       //.WithMany(t => t.EmployeesSlots)
       //.HasForeignKey(m => m.EmployeeId)
       //.OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Appointment>()
       .HasOne(p => p.Customer)
       .WithMany(t => t.CustomersAppointment)
       .HasForeignKey(m => m.CustomerId)
       .OnDelete(DeleteBehavior.Restrict);


            modelBuilder.Entity<Appointment>()
       .HasOne(p => p.Employee)
       .WithMany(t => t.EmployeesAppointment)
       .HasForeignKey(m => m.EmployeeId)
       .OnDelete(DeleteBehavior.Restrict);


        }



    }
}
