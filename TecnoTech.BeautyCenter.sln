
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.2.32505.173
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "TecnoTech.BeautyCenter.DAL.SqlServer", "TecnoTech.BeautyCenter.DAL.SqlServer\TecnoTech.BeautyCenter.DAL.SqlServer.csproj", "{37F1D810-1ADB-4DFC-93B0-7333AD188F7A}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "TecnoTech.BeautyCenter.Repository", "TecnoTech.BeautyCenter.Repository\TecnoTech.BeautyCenter.Repository.csproj", "{45639FE4-939A-4455-9972-E9D4445C74AA}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "TecnoTech.BeautyCenter.UnitOfWork", "TecnoTech.BeautyCenter.UnitOfWork\TecnoTech.BeautyCenter.UnitOfWork.csproj", "{1BF4B7CD-10D8-41DD-8F7A-3746C836395F}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "TecnoTech.BeautyCenter.Interface", "TecnoTech.BeautyCenter.Interface\TecnoTech.BeautyCenter.Interface.csproj", "{38BBA635-F2A8-40A6-90DB-59D2692993B2}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TecnoTech.BeautyCenter.DTOs", "TecnoTech.BeautyCenter.DTOs\TecnoTech.BeautyCenter.DTOs.csproj", "{76DD71CC-5ECF-40C5-9A7B-7D8223959DFF}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TecnoTech.BeautyCenter.Services", "TecnoTech.BeautyCenter.Services\TecnoTech.BeautyCenter.Services.csproj", "{AECAA13D-5A70-4815-8088-ECF292AB8EB5}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{37F1D810-1ADB-4DFC-93B0-7333AD188F7A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{37F1D810-1ADB-4DFC-93B0-7333AD188F7A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{37F1D810-1ADB-4DFC-93B0-7333AD188F7A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{37F1D810-1ADB-4DFC-93B0-7333AD188F7A}.Release|Any CPU.Build.0 = Release|Any CPU
		{45639FE4-939A-4455-9972-E9D4445C74AA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{45639FE4-939A-4455-9972-E9D4445C74AA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{45639FE4-939A-4455-9972-E9D4445C74AA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{45639FE4-939A-4455-9972-E9D4445C74AA}.Release|Any CPU.Build.0 = Release|Any CPU
		{1BF4B7CD-10D8-41DD-8F7A-3746C836395F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1BF4B7CD-10D8-41DD-8F7A-3746C836395F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1BF4B7CD-10D8-41DD-8F7A-3746C836395F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1BF4B7CD-10D8-41DD-8F7A-3746C836395F}.Release|Any CPU.Build.0 = Release|Any CPU
		{38BBA635-F2A8-40A6-90DB-59D2692993B2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{38BBA635-F2A8-40A6-90DB-59D2692993B2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{38BBA635-F2A8-40A6-90DB-59D2692993B2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{38BBA635-F2A8-40A6-90DB-59D2692993B2}.Release|Any CPU.Build.0 = Release|Any CPU
		{76DD71CC-5ECF-40C5-9A7B-7D8223959DFF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{76DD71CC-5ECF-40C5-9A7B-7D8223959DFF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{76DD71CC-5ECF-40C5-9A7B-7D8223959DFF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{76DD71CC-5ECF-40C5-9A7B-7D8223959DFF}.Release|Any CPU.Build.0 = Release|Any CPU
		{AECAA13D-5A70-4815-8088-ECF292AB8EB5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AECAA13D-5A70-4815-8088-ECF292AB8EB5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AECAA13D-5A70-4815-8088-ECF292AB8EB5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AECAA13D-5A70-4815-8088-ECF292AB8EB5}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {4DFD7908-4A5E-4C9E-B067-193B7BFF4BCA}
	EndGlobalSection
EndGlobal
